import { Server as SocketIOServer } from 'socket.io';
import { v4 as uuidv4 } from 'uuid';
import {
  Task,
  TaskStatus,
  TaskProcessingStage,
  AgentLog,
  DecisionLogEntry,
  LicenseInfo,
  FileNode,
  UserFeedback,
  AgentType,
  GeminiJsonPlannerTask,
  ProjectContext,
  UserAuthorshipDetails,
  LicenseType
} from '../../types.js';
import { BackendGeminiService } from './BackendGeminiService.js';
import { BackendPlannerAgent } from '../agents/BackendPlannerAgent.js';
import { AudioGeneratorAgent } from '../agents/AudioGeneratorAgent.js';
import { AssetManagerAgent } from '../agents/AssetManagerAgent.js';
import { DeploymentManagerAgent } from '../agents/DeploymentManagerAgent.js';
import { DependencyResolverAgent } from '../agents/DependencyResolverAgent.js';
import { QualityAssuranceAgent } from '../agents/QualityAssuranceAgent.js';
import { GameDesignerAgent } from '../agents/GameDesignerAgent.js';

/**
 * Interface for logging that sends updates to frontend via WebSocket
 */
interface BackendLoggingInterface {
  addCompanyLog: (agent: string, message: string, status: 'info' | 'success' | 'error' | 'working', taskId?: string) => Promise<void>;
  addTaskLog: (taskId: string, agent: string, message: string, status: 'info' | 'success' | 'error' | 'working', stage?: TaskProcessingStage, subDetailSections?: Array<{title: string, content: string, isCodeBlock?: boolean}>) => Promise<void>;
  addDecisionLogEntry: (agent: string, decision: string, reasoning: string, stackTrace?: string, taskId?: string) => Promise<void>;
}

/**
 * Service that handles the complete project generation workflow on the backend
 * This replaces the frontend logic that was causing memory issues
 */
export class ProjectGenerationService {
  private io: SocketIOServer;
  private geminiService: BackendGeminiService | null = null;
  private plannerAgent: BackendPlannerAgent | null = null;
  private audioGeneratorAgent: AudioGeneratorAgent | null = null;
  private assetManagerAgent: AssetManagerAgent | null = null;
  private deploymentManagerAgent: DeploymentManagerAgent | null = null;
  private dependencyResolverAgent: DependencyResolverAgent | null = null;
  private qualityAssuranceAgent: QualityAssuranceAgent | null = null;
  private gameDesignerAgent: GameDesignerAgent | null = null;
  private isGenerating = false;
  private currentProjectId: string | null = null;

  constructor(io: SocketIOServer) {
    this.io = io;
  }

  /**
   * Initialize the service with API key and create agents
   */
  public initializeWithApiKey(apiKey: string): void {
    this.geminiService = new BackendGeminiService(apiKey);

    const loggingInterface: BackendLoggingInterface = {
      addCompanyLog: this.addCompanyLog.bind(this),
      addTaskLog: async (taskId: string, agent: string, message: string, status: 'info' | 'success' | 'error' | 'working', stage?: TaskProcessingStage, subDetailSections?: Array<{title: string, content: string, isCodeBlock?: boolean}>) => {
        await this.addTaskLog(taskId, agent, message, status, stage || 'QUEUED', subDetailSections);
      },
      addDecisionLogEntry: async (agent: string, decision: string, reasoning: string, stackTrace?: string, taskId?: string) => {
        await this.addDecisionLogEntry(agent, decision, reasoning, stackTrace, taskId);
      }
    };

    // Initialize all agents
    this.plannerAgent = new BackendPlannerAgent(this.geminiService, loggingInterface);
    this.audioGeneratorAgent = new AudioGeneratorAgent(this.geminiService, loggingInterface);
    this.assetManagerAgent = new AssetManagerAgent(this.geminiService, loggingInterface);
    this.deploymentManagerAgent = new DeploymentManagerAgent(this.geminiService, loggingInterface);
    this.dependencyResolverAgent = new DependencyResolverAgent(this.geminiService, loggingInterface);
    this.qualityAssuranceAgent = new QualityAssuranceAgent(this.geminiService, loggingInterface);
    this.gameDesignerAgent = new GameDesignerAgent(this.geminiService, loggingInterface);
  }

  /**
   * Generate a complete project with full autonomy using all specialized agents
   */
  public async generateCompleteAutonomousProject(
    idea: string,
    userAuthorshipDetails: UserAuthorshipDetails,
    apiKey: string
  ): Promise<void> {
    if (this.isGenerating) {
      throw new Error('Project generation already in progress');
    }

    this.isGenerating = true;
    this.currentProjectId = uuidv4();

    try {
      this.initializeWithApiKey(apiKey);

      if (!this.plannerAgent || !this.dependencyResolverAgent || !this.assetManagerAgent ||
          !this.audioGeneratorAgent || !this.deploymentManagerAgent || !this.qualityAssuranceAgent) {
        throw new Error('Required agents not initialized');
      }

      await this.addCompanyLog('Project Manager', 'Starting complete autonomous project generation...', 'working');

      // Phase 1: Initial Planning (Enhanced for games if applicable)
      let plannerResponse;
      if (this.isGameProject(idea) && this.gameDesignerAgent) {
        await this.addCompanyLog('Project Manager', 'Detected game project - using specialized game design...', 'info');
        // Create a minimal ProjectContext for game design
        const gameProjectContext: ProjectContext = {
          id: this.currentProjectId || '',
          name: this.generateProjectName(idea),
          idea,
          fullContext: idea,
          apiKey: null,
          tasks: [],
          fileStructure: [],
          companyLogs: [],
          currentPhase: 'COMPANY_OPERATIONAL' as any,
          projectLifecycle: 'PLANNING' as any,
          currentFilePreview: null,
          activeTaskId: null,
          availableModels: [],
          agentModelConfiguration: {} as any,
          testingCycleCount: 0,
          decisionLog: [],
          devNotes: [],
          repeatedBugPatterns: [],
          architecturalSuggestions: [],
          lastModified: new Date().toISOString()
        };
        plannerResponse = await this.gameDesignerAgent.designGameSystems(
          gameProjectContext,
          'gemini-2.5-flash-preview-04-17'
        );
      } else {
        plannerResponse = await this.plannerAgent.generatePlan(
          idea,
          'gemini-2.5-flash-preview-04-17',
          idea,
          { type: LicenseType.MIT }
        );
      }

      // Create initial project structure
      const project = {
        id: this.currentProjectId,
        name: this.generateProjectName(idea),
        idea: idea,
        tasks: plannerResponse.tasks,
        fileStructure: plannerResponse.fileStructure || [],
        technologyStack: plannerResponse.technologyStackSuggestion,
        userAuthorshipDetails: userAuthorshipDetails,
        createdAt: new Date(),
        lastModified: new Date(),
        status: 'in-progress'
      };

      // Emit project created event
      this.io.emit('projectCreated', project);
      await this.addCompanyLog('Project Manager', `Project "${project.name}" created with ${project.tasks.length} tasks`, 'success');

      // Phase 2: Dependency Resolution
      await this.addCompanyLog('Project Manager', 'Resolving project dependencies...', 'working');
      const projectContext = this.createProjectContext(project);
      const dependencyResponse = await this.dependencyResolverAgent.resolveDependencies(
        projectContext,
        'gemini-2.5-flash-preview-04-17'
      );

      // Phase 3: Asset Management
      await this.addCompanyLog('Project Manager', 'Managing project assets...', 'working');
      const assetResponse = await this.assetManagerAgent.manageProjectAssets(
        projectContext,
        'gemini-2.5-flash-preview-04-17'
      );

      // Phase 4: Audio Generation (if needed)
      const audioRequirements = this.extractAudioRequirements(project);
      if (audioRequirements.length > 0) {
        await this.addCompanyLog('Project Manager', 'Generating audio assets...', 'working');
        const audioResponse = await this.audioGeneratorAgent.generateAudioAssets(
          projectContext,
          audioRequirements,
          'gemini-2.5-flash-preview-04-17'
        );
      }

      // Phase 5: Deployment Configuration
      await this.addCompanyLog('Project Manager', 'Configuring deployment...', 'working');
      const deploymentResponse = await this.deploymentManagerAgent.deployProject(
        projectContext,
        'gemini-2.5-flash-preview-04-17'
      );

      // Phase 6: Quality Assurance
      await this.addCompanyLog('Project Manager', 'Performing quality assurance...', 'working');
      const qaResponse = await this.qualityAssuranceAgent.performQualityAssurance(
        projectContext,
        'gemini-2.5-flash-preview-04-17'
      );

      // Update project with all enhancements
      project.lastModified = new Date();
      this.io.emit('projectUpdated', project);

      await this.addCompanyLog('Project Manager',
        `Complete autonomous project generation finished! Quality Score: ${qaResponse.overallScore}/100`,
        qaResponse.readinessStatus === 'ready' ? 'success' : 'info'
      );

    } catch (error) {
      this.isGenerating = false;
      this.currentProjectId = null;
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.addCompanyLog('Project Manager', `Error generating project: ${errorMessage}`, 'error');
      throw error;
    } finally {
      this.isGenerating = false;
      this.currentProjectId = null;
    }
  }

  /**
   * Start project generation workflow
   */
  public async startProjectGeneration(
    projectId: string,
    projectIdea: string,
    licenseInfo: LicenseInfo,
    agentModelConfiguration: Record<AgentType, string>
  ): Promise<void> {
    if (this.isGenerating) {
      throw new Error('Project generation already in progress');
    }

    if (!this.geminiService || !this.plannerAgent) {
      throw new Error('Service not initialized with API key');
    }

    this.isGenerating = true;
    this.currentProjectId = projectId;

    try {
      await this.addCompanyLog('System', `Starting project generation for: ${projectIdea}`, 'working');

      // Phase 1: Generate initial plan
      await this.addCompanyLog('Planner Agent', 'Generating initial project plan...', 'working');
      
      const plannerModel = agentModelConfiguration[AgentType.PLANNER];
      const projectContext = `Project ID: ${projectId}\nProject Idea: ${projectIdea}\nLicense: ${licenseInfo.type}`;
      
      const initialPlan = await this.plannerAgent.generatePlan(
        projectIdea,
        plannerModel,
        projectContext,
        licenseInfo
      );

      await this.addCompanyLog('Planner Agent', `Initial plan generated with ${initialPlan.tasks.length} tasks`, 'success');
      await this.addDecisionLogEntry('Planner Agent', 'Initial Plan Generated', `Created ${initialPlan.tasks.length} tasks and ${initialPlan.fileStructure?.length || 0} file structure items`);

      // Emit initial plan to frontend
      this.io.emit('project-generation-update', {
        projectId,
        phase: 'planning',
        data: {
          tasks: initialPlan.tasks,
          fileStructure: initialPlan.fileStructure,
          technologyStackSuggestion: initialPlan.technologyStackSuggestion
        }
      });

      // Phase 2: Review and refine plan
      await this.addCompanyLog('Planner Agent', 'Reviewing and refining initial plan...', 'working');
      
      const refinedPlan = await this.plannerAgent.reviewAndRefinePlan(
        projectIdea,
        initialPlan.tasks,
        initialPlan.fileStructure || [],
        projectContext,
        plannerModel,
        initialPlan.technologyStackSuggestion
      );

      await this.addCompanyLog('Planner Agent', 'Plan reviewed and finalized', 'success');
      await this.addDecisionLogEntry('Planner Agent', 'Plan Reviewed & Finalized', `Final plan: ${refinedPlan.tasks.length} tasks, ${refinedPlan.fileStructure?.length || 0} files`);

      // Convert to full Task objects
      const finalTasks: Task[] = refinedPlan.tasks.map((t: GeminiJsonPlannerTask) => ({
        ...t,
        id: t.id || uuidv4(),
        status: 'pending' as TaskStatus,
        currentProcessingStage: 'QUEUED' as TaskProcessingStage,
        agentMessages: [],
        identifiedBugs: [],
        unresolvedBugs: [],
        bugFixingCycles: 0
      }));

      // Convert to full FileNode objects
      const finalFileStructure: FileNode[] = (refinedPlan.fileStructure || []).map((n: Omit<FileNode, 'id' | 'path' | 'children' | 'isTestFile'> & { children?: Array<Omit<FileNode, 'id' | 'path' | 'isTestFile'>> }) => ({
        ...n,
        id: uuidv4(),
        path: n.name,
        children: n.children?.map((c: Omit<FileNode, 'id' | 'path' | 'isTestFile'>) => ({
          ...c,
          id: uuidv4(),
          path: `${n.name}/${c.name}`,
          children: []
        })) || []
      }));

      // Emit final plan to frontend
      this.io.emit('project-generation-update', {
        projectId,
        phase: 'plan-finalized',
        data: {
          tasks: finalTasks,
          fileStructure: finalFileStructure,
          technologyStackSuggestion: refinedPlan.technologyStackSuggestion,
          reviewNotes: refinedPlan.reviewNotes
        }
      });

      await this.addCompanyLog('System', 'Project generation completed successfully', 'success');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.addCompanyLog('System', `Project generation failed: ${errorMessage}`, 'error');
      
      this.io.emit('project-generation-error', {
        projectId,
        error: errorMessage
      });
      
      throw error;
    } finally {
      this.isGenerating = false;
      this.currentProjectId = null;
    }
  }

  /**
   * Process user feedback and generate additional tasks
   */
  public async processUserFeedback(
    _projectId: string,
    projectContext: string,
    projectIdea: string,
    fileStructure: FileNode[],
    feedback: UserFeedback,
    modelName: string
  ): Promise<Task[]> {
    if (!this.plannerAgent) {
      throw new Error('Service not initialized');
    }

    await this.addCompanyLog('Planner Agent', `Processing user feedback: ${feedback.description.substring(0, 50)}...`, 'working');

    const feedbackResponse = await this.plannerAgent.generateTasksFromFeedback(
      projectContext,
      projectIdea,
      fileStructure,
      feedback,
      modelName
    );

    const newTasks: Task[] = feedbackResponse.tasks.map((t: GeminiJsonPlannerTask) => ({
      ...t,
      id: t.id || uuidv4(),
      status: 'pending' as TaskStatus,
      currentProcessingStage: 'QUEUED' as TaskProcessingStage,
      agentMessages: [],
      identifiedBugs: [],
      unresolvedBugs: [],
      bugFixingCycles: 0,
      purpose: 'user-feedback-driven'
    }));

    await this.addCompanyLog('Planner Agent', `Generated ${newTasks.length} tasks from user feedback`, 'success');
    await this.addDecisionLogEntry('Planner Agent', 'Tasks Generated from User Feedback', `Generated ${newTasks.length} tasks to address user feedback`);

    return newTasks;
  }

  /**
   * Get current generation status
   */
  public getGenerationStatus(): { isGenerating: boolean; projectId: string | null } {
    return {
      isGenerating: this.isGenerating,
      projectId: this.currentProjectId
    };
  }

  /**
   * Add company log and emit to frontend
   */
  private async addCompanyLog(agent: string, message: string, status: 'info' | 'success' | 'error' | 'working', _taskId?: string): Promise<void> {
    const logEntry: AgentLog = {
      id: uuidv4(),
      timestamp: new Date(),
      agent,
      message,
      status
    };

    this.io.emit('company-log', logEntry);
    console.log(`[${agent}] ${message}`);
  }

  /**
   * Add task log and emit to frontend
   */
  private async addTaskLog(
    taskId: string, 
    agent: string, 
    message: string, 
    status: 'info' | 'success' | 'error' | 'working', 
    stage: TaskProcessingStage, 
    attachments?: Array<{title: string, content: string, isCodeBlock?: boolean}>
  ): Promise<void> {
    const logEntry = {
      taskId,
      agent,
      message,
      status,
      stage,
      attachments,
      timestamp: new Date()
    };

    this.io.emit('task-log', logEntry);
    console.log(`[${agent}] Task ${taskId}: ${message}`);
  }

  /**
   * Add decision log entry and emit to frontend
   */
  private async addDecisionLogEntry(agent: string, decision: string, reasoning: string, stackTrace?: string, taskId?: string): Promise<void> {
    // Map string agent names to proper types
    let agentType: AgentType | 'System' | 'ProjectManager' | 'User';
    switch (agent) {
      case 'Planner Agent':
        agentType = AgentType.PLANNER;
        break;
      case 'Coder Agent':
        agentType = AgentType.CODER;
        break;
      case 'Context Manager Agent':
        agentType = AgentType.CONTEXT_MANAGER;
        break;
      case 'System':
        agentType = 'System';
        break;
      default:
        agentType = 'System'; // Default fallback
    }

    const entry: DecisionLogEntry = {
      id: uuidv4(),
      timestamp: new Date(),
      agent: agentType,
      action: decision,
      details: reasoning,
      reason: stackTrace,
      taskId
    };

    this.io.emit('decision-log', entry);
    console.log(`[${agent}] Decision: ${decision} - ${reasoning}`);
  }

  /**
   * Helper methods for autonomous project generation
   */
  private isGameProject(idea: string): boolean {
    const gameKeywords = ['game', 'rpg', 'tower', 'platformer', 'puzzle', 'strategy', 'shooter', 'racing', 'card', 'simulation'];
    const lowerIdea = idea.toLowerCase();
    return gameKeywords.some(keyword => lowerIdea.includes(keyword));
  }

  private createProjectContext(project: any): ProjectContext {
    return {
      idea: project.idea,
      fullContext: `Project: ${project.name}\nIdea: ${project.idea}\nTechnology Stack: ${project.technologyStack}`,
      name: project.name,
      tasks: project.tasks,
      fileStructure: project.fileStructure,
      suggestedTechnologyStack: project.technologyStack
    } as ProjectContext;
  }

  private generateProjectName(idea: string): string {
    // Simple project name generation from idea
    const words = idea.split(' ').slice(0, 3);
    return words.map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  }

  private extractAudioRequirements(project: any): string[] {
    const requirements: string[] = [];
    const idea = project.idea.toLowerCase();

    if (this.isGameProject(idea)) {
      requirements.push('Background music for gameplay');
      requirements.push('Sound effects for user interactions');
      requirements.push('Ambient sounds for atmosphere');

      if (idea.includes('rpg')) {
        requirements.push('Battle music');
        requirements.push('Victory fanfare');
        requirements.push('Menu navigation sounds');
      }
    } else if (idea.includes('app') || idea.includes('website')) {
      requirements.push('UI interaction sounds');
      requirements.push('Notification sounds');
    }

    return requirements;
  }
}
