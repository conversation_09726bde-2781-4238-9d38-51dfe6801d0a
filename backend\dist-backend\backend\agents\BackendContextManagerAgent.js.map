{"version": 3, "file": "BackendContextManagerAgent.js", "sourceRoot": "", "sources": ["../../../agents/BackendContextManagerAgent.ts"], "names": [], "mappings": "AAoBA;;;GAGG;AACH,MAAM,OAAO,0BAA0B;IAIrC,YAAY,aAAmC,EAAE,gBAAyC;QACxF,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;IAC3C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW,CAAC,OAAe,EAAE,MAAgD;QACzF,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,uBAAuB,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACtF,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,QAAgB,EAAE,SAAiB,EAAE,UAAmB,EAAE,MAAe;QAC3F,kDAAkD;QAClD,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,uBAAuB,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;IAC9G,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,oBAAoB,CAC/B,cAAsB,EACtB,cAAsB,EACtB,SAAiB;QAEjB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,WAAW,CAAC,kDAAkD,EAAE,SAAS,CAAC,CAAC;YACtF,IAAI,CAAC,WAAW,CAAC,wBAAwB,EAAE,2BAA2B,cAAc,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,EAAE,gBAAgB,SAAS,EAAE,CAAC,CAAC;YAE1I,MAAM,cAAc,GAAG;;UAEnB,cAAc;;;UAGd,cAAc;;;;;;;;;OASjB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAC5D,SAAS,EACT,cAAc,EACd,+MAA+M,EAC/M,CAAC,IAAS,EAAiC,EAAE;gBAC3C,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI;oBACzC,gBAAgB,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,cAAc,KAAK,QAAQ;oBACnE,CAAC,OAAO,IAAI,CAAC,OAAO,KAAK,WAAW,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC;YACnF,CAAC,EACD,GAAG,CACJ,CAAC;YAEF,MAAM,IAAI,CAAC,WAAW,CAAC,sCAAsC,EAAE,SAAS,CAAC,CAAC;YAC1E,IAAI,CAAC,WAAW,CAAC,0BAA0B,EAAE,kDAAkD,QAAQ,CAAC,OAAO,IAAI,qBAAqB,EAAE,EAAE,uCAAuC,CAAC,CAAC;YAErL,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,CAAC,WAAW,CAAC,qCAAqC,YAAY,EAAE,EAAE,OAAO,CAAC,CAAC;YACrF,IAAI,CAAC,WAAW,CAAC,uBAAuB,EAAE,UAAU,YAAY,EAAE,EAAE,qCAAqC,CAAC,CAAC;YAC3G,OAAO,CAAC,KAAK,CAAC,sDAAsD,EAAE,KAAK,CAAC,CAAC;YAC7E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,qBAAqB,CAChC,cAAsB,EACtB,SAAiB;QAMjB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,WAAW,CAAC,2CAA2C,EAAE,SAAS,CAAC,CAAC;YAC/E,IAAI,CAAC,WAAW,CAAC,0BAA0B,EAAE,oEAAoE,EAAE,gBAAgB,SAAS,EAAE,CAAC,CAAC;YAEhJ,MAAM,cAAc,GAAG;;UAEnB,cAAc;;;;;;;;;;;OAWjB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAC5D,SAAS,EACT,cAAc,EACd,yNAAyN,EACzN,CAAC,IAAS,EAAwF,EAAE;gBAClG,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI;oBACzC,UAAU,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;oBAClD,iBAAiB,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC;oBAChE,iBAAiB,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC;oBAChE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC;oBAC5D,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC;oBACnE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC;YAC7E,CAAC,EACD,GAAG,CACJ,CAAC;YAEF,MAAM,IAAI,CAAC,WAAW,CAAC,qCAAqC,QAAQ,CAAC,QAAQ,CAAC,MAAM,cAAc,QAAQ,CAAC,eAAe,CAAC,MAAM,qBAAqB,QAAQ,CAAC,eAAe,CAAC,MAAM,mBAAmB,EAAE,SAAS,CAAC,CAAC;YACrN,IAAI,CAAC,WAAW,CAAC,4BAA4B,EAAE,sBAAsB,QAAQ,CAAC,QAAQ,CAAC,MAAM,cAAc,QAAQ,CAAC,eAAe,CAAC,MAAM,qBAAqB,QAAQ,CAAC,eAAe,CAAC,MAAM,oBAAoB,EAAE,yCAAyC,CAAC,CAAC;YAE/P,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,CAAC,WAAW,CAAC,sCAAsC,YAAY,EAAE,EAAE,OAAO,CAAC,CAAC;YACtF,IAAI,CAAC,WAAW,CAAC,yBAAyB,EAAE,UAAU,YAAY,EAAE,EAAE,uCAAuC,CAAC,CAAC;YAC/G,OAAO,CAAC,KAAK,CAAC,uDAAuD,EAAE,KAAK,CAAC,CAAC;YAC9E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,uBAAuB,CAClC,cAAsB,EACtB,SAAiB;QAMjB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,WAAW,CAAC,uCAAuC,EAAE,SAAS,CAAC,CAAC;YAC3E,IAAI,CAAC,WAAW,CAAC,yBAAyB,EAAE,+CAA+C,EAAE,gBAAgB,SAAS,EAAE,CAAC,CAAC;YAE1H,MAAM,cAAc,GAAG;;UAEnB,cAAc;;;;;;;;;;;OAWjB,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAC5D,SAAS,EACT,cAAc,EACd,2IAA2I,EAC3I,CAAC,IAAS,EAAoE,EAAE;gBAC9E,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI;oBACzC,SAAS,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ;oBACrD,WAAW,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC;oBACpD,QAAQ,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ;oBACnD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC;YACvE,CAAC,EACD,GAAG,CACJ,CAAC;YAEF,MAAM,IAAI,CAAC,WAAW,CAAC,gDAAgD,EAAE,SAAS,CAAC,CAAC;YACpF,IAAI,CAAC,WAAW,CAAC,2BAA2B,EAAE,0BAA0B,QAAQ,CAAC,SAAS,CAAC,MAAM,wBAAwB,QAAQ,CAAC,MAAM,EAAE,EAAE,wCAAwC,CAAC,CAAC;YAEtL,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,CAAC,WAAW,CAAC,wCAAwC,YAAY,EAAE,EAAE,OAAO,CAAC,CAAC;YACxF,IAAI,CAAC,WAAW,CAAC,wBAAwB,EAAE,UAAU,YAAY,EAAE,EAAE,sCAAsC,CAAC,CAAC;YAC7G,OAAO,CAAC,KAAK,CAAC,yDAAyD,EAAE,KAAK,CAAC,CAAC;YAChF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF"}