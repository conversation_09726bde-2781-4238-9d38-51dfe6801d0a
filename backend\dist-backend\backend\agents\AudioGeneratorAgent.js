import { AgentType } from '../../types.js';
/**
 * Audio Generator Agent - Handles autonomous audio asset generation and integration
 * Integrates with free audio APIs and AI audio generation services
 */
export class AudioGeneratorAgent {
    constructor(geminiService, loggingInterface) {
        this.geminiService = geminiService;
        this.loggingInterface = loggingInterface;
    }
    /**
     * Log activity to the frontend
     */
    async logActivity(message, status) {
        await this.loggingInterface.addCompanyLog('Audio Generator Agent', message, status);
    }
    /**
     * Log decision to the frontend
     */
    logDecision(decision, reasoning, stackTrace, taskId) {
        this.loggingInterface.addDecisionLogEntry(AgentType.AUDIO_GENERATOR, decision, reasoning, stackTrace, taskId);
    }
    /**
     * Generate audio assets for a project based on requirements
     */
    async generateAudioAssets(projectContext, audioRequirements, modelName, taskId) {
        await this.logActivity('Analyzing audio requirements and generating assets...', 'working');
        try {
            const prompt = this.buildAudioGenerationPrompt(projectContext, audioRequirements);
            const response = await this.geminiService.makeRequestWithRetry(modelName, prompt, this.getSystemInstruction(), this.validateAudioGeneratorResponse.bind(this), 0.7);
            // Process and acquire actual audio files
            const processedAssets = await this.acquireAudioAssets(response.audioAssets, projectContext);
            response.audioAssets = processedAssets;
            // Generate integration code
            response.integrationCode = this.generateAudioIntegrationCode(processedAssets, projectContext);
            await this.logActivity(`Generated ${processedAssets.length} audio assets successfully`, 'success');
            this.logDecision('Audio Assets Generated', `Generated ${processedAssets.length} audio assets including music, sound effects, and ambient sounds`, undefined, taskId);
            return response;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            await this.logActivity(`Error generating audio assets: ${errorMessage}`, 'error');
            throw error;
        }
    }
    /**
     * Build the prompt for audio generation
     */
    buildAudioGenerationPrompt(projectContext, requirements) {
        const projectType = this.inferProjectType(projectContext);
        return `
Project Context: ${projectContext.fullContext}
Project Type: ${projectType}
Audio Requirements: ${requirements.join(', ')}

Generate a comprehensive audio asset plan for this project. Consider:

1. **Background Music**: Appropriate for the project type and mood
2. **Sound Effects**: Interactive elements, UI sounds, game mechanics
3. **Ambient Sounds**: Environmental audio to enhance immersion
4. **Voice/Narration**: If applicable to the project

For each audio asset, specify:
- Type (music/sfx/ambient/voice)
- Purpose and context of use
- Duration (approximate)
- Style/mood description
- Technical specifications (format, quality)

Prioritize assets that are:
- Essential for core functionality
- Enhance user experience
- Match the project's theme and style
- Technically feasible to generate or source

Available audio sources: Freesound API, MusicGen AI, Zapsplat Free Tier
Supported formats: MP3, WAV, OGG
`;
    }
    /**
     * Acquire actual audio files from various sources
     */
    async acquireAudioAssets(assets, projectContext) {
        const processedAssets = [];
        for (const asset of assets) {
            try {
                let acquiredAsset;
                switch (asset.type) {
                    case 'music':
                        acquiredAsset = await this.generateMusic(asset);
                        break;
                    case 'sfx':
                        acquiredAsset = await this.findSoundEffect(asset);
                        break;
                    case 'ambient':
                        acquiredAsset = await this.findAmbientSound(asset);
                        break;
                    case 'voice':
                        acquiredAsset = await this.generateVoice(asset);
                        break;
                    default:
                        acquiredAsset = await this.createPlaceholderAudio(asset);
                }
                processedAssets.push(acquiredAsset);
                await this.logActivity(`Acquired audio asset: ${asset.name}`, 'info');
            }
            catch (error) {
                await this.logActivity(`Failed to acquire ${asset.name}, using placeholder`, 'error');
                processedAssets.push(await this.createPlaceholderAudio(asset));
            }
        }
        return processedAssets;
    }
    /**
     * Generate music using AI services
     */
    async generateMusic(asset) {
        // Implementation for MusicGen or other AI music generation
        // For now, return a placeholder with proper structure
        return {
            ...asset,
            url: `https://example.com/generated-music/${asset.id}.mp3`,
            localPath: `assets/audio/music/${asset.id}.mp3`,
            license: 'Generated',
            attribution: 'AI Generated Music'
        };
    }
    /**
     * Find sound effects from Freesound API
     */
    async findSoundEffect(asset) {
        // Implementation for Freesound API integration
        return {
            ...asset,
            url: `https://freesound.org/data/previews/${asset.id}.mp3`,
            localPath: `assets/audio/sfx/${asset.id}.mp3`,
            license: 'CC BY 3.0',
            attribution: 'Freesound.org'
        };
    }
    /**
     * Find ambient sounds
     */
    async findAmbientSound(asset) {
        return {
            ...asset,
            url: `https://example.com/ambient/${asset.id}.mp3`,
            localPath: `assets/audio/ambient/${asset.id}.mp3`,
            license: 'CC0',
            attribution: 'Public Domain'
        };
    }
    /**
     * Generate voice/narration
     */
    async generateVoice(asset) {
        return {
            ...asset,
            url: `https://example.com/voice/${asset.id}.mp3`,
            localPath: `assets/audio/voice/${asset.id}.mp3`,
            license: 'Generated',
            attribution: 'AI Generated Voice'
        };
    }
    /**
     * Create placeholder audio for fallback
     */
    async createPlaceholderAudio(asset) {
        return {
            ...asset,
            url: `data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT`,
            localPath: `assets/audio/placeholder/${asset.id}.wav`,
            license: 'Placeholder',
            attribution: 'Generated Placeholder'
        };
    }
    /**
     * Generate audio integration code
     */
    generateAudioIntegrationCode(assets, projectContext) {
        const projectType = this.inferProjectType(projectContext);
        if (projectType.includes('React') || projectType.includes('web')) {
            return this.generateWebAudioCode(assets);
        }
        else if (projectType.includes('game')) {
            return this.generateGameAudioCode(assets);
        }
        else {
            return this.generateGenericAudioCode(assets);
        }
    }
    /**
     * Generate web audio integration code
     */
    generateWebAudioCode(assets) {
        return `
// Audio Manager for Web Application
class AudioManager {
  constructor() {
    this.sounds = new Map();
    this.musicVolume = 0.7;
    this.sfxVolume = 0.8;
    this.loadAudioAssets();
  }

  async loadAudioAssets() {
    const audioAssets = ${JSON.stringify(assets, null, 2)};
    
    for (const asset of audioAssets) {
      try {
        const audio = new Audio(asset.localPath);
        audio.preload = 'auto';
        this.sounds.set(asset.id, {
          audio,
          type: asset.type,
          loaded: false
        });
        
        audio.addEventListener('canplaythrough', () => {
          this.sounds.get(asset.id).loaded = true;
        });
      } catch (error) {
        console.warn(\`Failed to load audio asset: \${asset.name}\`, error);
      }
    }
  }

  play(assetId, loop = false, volume = null) {
    const sound = this.sounds.get(assetId);
    if (!sound || !sound.loaded) return;

    const audio = sound.audio.cloneNode();
    audio.loop = loop;
    audio.volume = volume || (sound.type === 'music' ? this.musicVolume : this.sfxVolume);
    audio.play().catch(console.warn);
    
    return audio;
  }

  setMusicVolume(volume) {
    this.musicVolume = Math.max(0, Math.min(1, volume));
  }

  setSfxVolume(volume) {
    this.sfxVolume = Math.max(0, Math.min(1, volume));
  }
}

export const audioManager = new AudioManager();
`;
    }
    /**
     * Generate game audio integration code
     */
    generateGameAudioCode(assets) {
        return `
// Game Audio System
class GameAudioSystem {
  constructor() {
    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
    this.masterGain = this.audioContext.createGain();
    this.masterGain.connect(this.audioContext.destination);
    
    this.musicGain = this.audioContext.createGain();
    this.sfxGain = this.audioContext.createGain();
    this.musicGain.connect(this.masterGain);
    this.sfxGain.connect(this.masterGain);
    
    this.audioBuffers = new Map();
    this.loadGameAudio();
  }

  async loadGameAudio() {
    const audioAssets = ${JSON.stringify(assets, null, 2)};
    
    for (const asset of audioAssets) {
      try {
        const response = await fetch(asset.localPath);
        const arrayBuffer = await response.arrayBuffer();
        const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
        this.audioBuffers.set(asset.id, {
          buffer: audioBuffer,
          type: asset.type
        });
      } catch (error) {
        console.warn(\`Failed to load game audio: \${asset.name}\`, error);
      }
    }
  }

  playSound(assetId, loop = false, volume = 1) {
    const audioData = this.audioBuffers.get(assetId);
    if (!audioData) return;

    const source = this.audioContext.createBufferSource();
    const gainNode = this.audioContext.createGain();
    
    source.buffer = audioData.buffer;
    source.loop = loop;
    gainNode.gain.value = volume;
    
    source.connect(gainNode);
    gainNode.connect(audioData.type === 'music' ? this.musicGain : this.sfxGain);
    
    source.start();
    return source;
  }
}

export const gameAudio = new GameAudioSystem();
`;
    }
    /**
     * Generate generic audio integration code
     */
    generateGenericAudioCode(assets) {
        return `
// Generic Audio Integration
const audioAssets = ${JSON.stringify(assets, null, 2)};

function loadAudio(assetId) {
  const asset = audioAssets.find(a => a.id === assetId);
  if (!asset) return null;
  
  const audio = new Audio(asset.localPath);
  return audio;
}

function playAudio(assetId, options = {}) {
  const audio = loadAudio(assetId);
  if (!audio) return;
  
  audio.volume = options.volume || 0.7;
  audio.loop = options.loop || false;
  audio.play().catch(console.warn);
  
  return audio;
}
`;
    }
    /**
     * Infer project type from context
     */
    inferProjectType(projectContext) {
        const context = projectContext.fullContext.toLowerCase();
        const idea = projectContext.idea.toLowerCase();
        if (context.includes('game') || idea.includes('game'))
            return 'game';
        if (context.includes('react') || context.includes('web app'))
            return 'React web app';
        if (context.includes('mobile'))
            return 'mobile app';
        return 'web application';
    }
    /**
     * Validate audio generator response
     */
    validateAudioGeneratorResponse(data) {
        return (typeof data === 'object' &&
            data !== null &&
            'audioAssets' in data &&
            Array.isArray(data.audioAssets) &&
            data.audioAssets.every((asset) => typeof asset === 'object' &&
                'id' in asset &&
                'name' in asset &&
                'type' in asset &&
                ['music', 'sfx', 'ambient', 'voice'].includes(asset.type)));
    }
    /**
     * Get system instruction for audio generation
     */
    getSystemInstruction() {
        return `You are an Audio Generator Agent responsible for creating comprehensive audio asset plans for projects.

Your task is to analyze project requirements and generate a detailed audio asset specification that includes:

1. **Background Music**: Appropriate musical tracks for different contexts
2. **Sound Effects**: Interactive and feedback sounds
3. **Ambient Audio**: Environmental and atmospheric sounds
4. **Voice/Narration**: Spoken content if applicable

For each audio asset, provide:
- Unique ID and descriptive name
- Type (music/sfx/ambient/voice)
- Purpose and usage context
- Approximate duration
- Style/mood description
- Technical format preference

Consider the project type, target audience, and technical constraints. Prioritize essential audio that enhances user experience and supports core functionality.

Respond with a JSON object containing:
- "audioAssets": Array of audio asset specifications
- "audioManifest": Summary of total assets, duration, and formats

Ensure all audio assets are feasible to generate or source from free/open-source APIs.`;
    }
}
//# sourceMappingURL=AudioGeneratorAgent.js.map