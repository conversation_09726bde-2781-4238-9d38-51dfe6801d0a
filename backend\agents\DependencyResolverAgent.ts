import {
  GeminiJsonDependencyResolverResponse,
  DependencyInfo,
  ProjectContext,
  AgentType
} from '../../types.js';
import { BackendGeminiService } from '../services/BackendGeminiService.js';

/**
 * Interface for backend logging that sends updates to frontend
 */
interface BackendLoggingInterface {
  addCompanyLog: (agent: string, message: string, status: 'info' | 'success' | 'error' | 'working', taskId?: string) => Promise<void>;
  addTaskLog: (taskId: string, agent: string, message: string, status: 'info' | 'success' | 'error' | 'working', stage?: any, subDetailSections?: Array<{title: string, content: string, isCodeBlock?: boolean}>) => Promise<void>;
  addDecisionLogEntry: (agent: string, decision: string, reasoning: string, stackTrace?: string, taskId?: string) => Promise<void>;
}

/**
 * Dependency Resolver Agent - Handles autonomous dependency management
 * Automatically identifies, resolves, and configures all project dependencies
 */
export class DependencyResolverAgent {
  constructor(
    private geminiService: BackendGeminiService,
    private loggingInterface: BackendLoggingInterface
  ) {}

  /**
   * Log activity to the frontend
   */
  private async logActivity(message: string, status: 'info' | 'success' | 'error' | 'working'): Promise<void> {
    await this.loggingInterface.addCompanyLog('Dependency Resolver Agent', message, status);
  }

  /**
   * Log decision to the frontend
   */
  private logDecision(decision: string, reasoning: string, stackTrace?: string, taskId?: string): void {
    this.loggingInterface.addDecisionLogEntry(AgentType.DEPENDENCY_RESOLVER, decision, reasoning, stackTrace, taskId);
  }

  /**
   * Resolve all dependencies for a project
   */
  public async resolveDependencies(
    projectContext: ProjectContext,
    modelName: string,
    taskId?: string
  ): Promise<GeminiJsonDependencyResolverResponse> {
    await this.logActivity('Analyzing project dependencies...', 'working');
    
    try {
      // Analyze project and identify all required dependencies
      const dependencies = await this.analyzeDependencies(projectContext, modelName);
      
      // Generate installation script
      const installationScript = this.generateInstallationScript(dependencies);
      
      // Generate configuration files
      const configurationFiles = this.generateConfigurationFiles(dependencies, projectContext);
      
      // Generate environment variables
      const environmentVariables = this.generateEnvironmentVariables(dependencies, projectContext);

      const response: GeminiJsonDependencyResolverResponse = {
        dependencies,
        installationScript,
        configurationFiles,
        environmentVariables
      };

      await this.logActivity(`Resolved ${dependencies.length} dependencies successfully`, 'success');
      this.logDecision(
        'Dependencies Resolved',
        `Identified and configured ${dependencies.length} dependencies including libraries, frameworks, and services`,
        undefined,
        taskId
      );

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Error resolving dependencies: ${errorMessage}`, 'error');
      throw error;
    }
  }

  /**
   * Analyze project and identify all required dependencies
   */
  private async analyzeDependencies(
    projectContext: ProjectContext,
    modelName: string
  ): Promise<DependencyInfo[]> {
    const prompt = this.buildDependencyAnalysisPrompt(projectContext);
    
    const response = await this.geminiService.makeRequestWithRetry<{dependencies: DependencyInfo[]}>(
      modelName,
      prompt,
      this.getDependencyAnalysisSystemInstruction(),
      (data: any) => this.validateDependencies(data),
      0.5
    );

    return response.dependencies;
  }

  /**
   * Build prompt for dependency analysis
   */
  private buildDependencyAnalysisPrompt(projectContext: ProjectContext): string {
    const projectType = this.inferProjectType(projectContext);
    
    return `
Project Context: ${projectContext.fullContext}
Project Type: ${projectType}
File Structure: ${JSON.stringify(projectContext.fileStructure, null, 2)}
Suggested Technology Stack: ${projectContext.suggestedTechnologyStack || 'Not specified'}

Analyze this project and identify ALL dependencies required for complete functionality:

1. **Core Framework Dependencies**: React, Vue, Angular, Express, etc.
2. **Build Tools**: Vite, Webpack, Babel, TypeScript, etc.
3. **Styling**: CSS frameworks, preprocessors, component libraries
4. **State Management**: Redux, Zustand, Context API, etc.
5. **Routing**: React Router, Vue Router, etc.
6. **HTTP Clients**: Axios, Fetch API wrappers, etc.
7. **Testing**: Jest, Vitest, Testing Library, etc.
8. **Utilities**: Lodash, Date-fns, UUID, etc.
9. **Game Libraries**: (if applicable) Phaser, Three.js, etc.
10. **Audio Libraries**: (if applicable) Howler.js, Web Audio API wrappers
11. **Database**: (if applicable) SQLite, MongoDB drivers, ORMs
12. **Authentication**: (if applicable) JWT libraries, OAuth clients
13. **Deployment Tools**: Build tools, CLI tools for deployment

For each dependency, specify:
- Name and version
- Type (npm/cdn/local/api/service)
- Purpose and why it's needed
- Whether it's required or optional
- Installation command
- Any configuration requirements

Prioritize stable, well-maintained packages with good documentation.
`;
  }

  /**
   * Generate installation script
   */
  private generateInstallationScript(dependencies: DependencyInfo[]): string {
    const npmDeps = dependencies.filter(dep => dep.type === 'npm');
    const prodDeps = npmDeps.filter(dep => dep.required).map(dep => `${dep.name}@${dep.version}`);
    const devDeps = npmDeps.filter(dep => !dep.required).map(dep => `${dep.name}@${dep.version}`);

    let script = '#!/bin/bash\n\n';
    script += '# Dependency Installation Script\n';
    script += '# Generated by DevGenius Studio Dependency Resolver\n\n';
    
    script += 'echo "Installing project dependencies..."\n\n';
    
    if (prodDeps.length > 0) {
      script += '# Production dependencies\n';
      script += `npm install ${prodDeps.join(' ')}\n\n`;
    }
    
    if (devDeps.length > 0) {
      script += '# Development dependencies\n';
      script += `npm install --save-dev ${devDeps.join(' ')}\n\n`;
    }

    // Add CDN and other types
    const cdnDeps = dependencies.filter(dep => dep.type === 'cdn');
    if (cdnDeps.length > 0) {
      script += '# CDN dependencies (add to HTML):\n';
      cdnDeps.forEach(dep => {
        script += `# <script src="https://cdn.jsdelivr.net/npm/${dep.name}@${dep.version}"></script>\n`;
      });
      script += '\n';
    }

    script += 'echo "Dependencies installed successfully!"\n';
    return script;
  }

  /**
   * Generate configuration files
   */
  private generateConfigurationFiles(dependencies: DependencyInfo[], projectContext: ProjectContext): Array<{path: string; content: string}> {
    const configFiles: Array<{path: string; content: string}> = [];
    
    // Generate package.json updates
    const packageJsonUpdates = this.generatePackageJsonUpdates(dependencies);
    if (packageJsonUpdates) {
      configFiles.push({
        path: 'package.json',
        content: packageJsonUpdates
      });
    }

    // Generate TypeScript config if needed
    if (dependencies.some(dep => dep.name === 'typescript')) {
      configFiles.push({
        path: 'tsconfig.json',
        content: this.generateTypeScriptConfig(dependencies, projectContext)
      });
    }

    // Generate Vite config if needed
    if (dependencies.some(dep => dep.name === 'vite')) {
      configFiles.push({
        path: 'vite.config.ts',
        content: this.generateViteConfig(dependencies, projectContext)
      });
    }

    // Generate ESLint config if needed
    if (dependencies.some(dep => dep.name.includes('eslint'))) {
      configFiles.push({
        path: '.eslintrc.json',
        content: this.generateESLintConfig(dependencies)
      });
    }

    return configFiles;
  }

  /**
   * Generate package.json updates
   */
  private generatePackageJsonUpdates(dependencies: DependencyInfo[]): string {
    const npmDeps = dependencies.filter(dep => dep.type === 'npm');
    const prodDeps: Record<string, string> = {};
    const devDeps: Record<string, string> = {};

    npmDeps.forEach(dep => {
      if (dep.required) {
        prodDeps[dep.name] = dep.version;
      } else {
        devDeps[dep.name] = dep.version;
      }
    });

    const packageJson = {
      dependencies: prodDeps,
      devDependencies: devDeps,
      scripts: {
        dev: 'vite',
        build: 'tsc && vite build',
        preview: 'vite preview',
        test: 'vitest',
        lint: 'eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0'
      }
    };

    return JSON.stringify(packageJson, null, 2);
  }

  /**
   * Generate TypeScript configuration
   */
  private generateTypeScriptConfig(dependencies: DependencyInfo[], projectContext: ProjectContext): string {
    const config = {
      compilerOptions: {
        target: 'ESNext',
        useDefineForClassFields: true,
        lib: ['DOM', 'DOM.Iterable', 'ESNext'],
        allowJs: false,
        skipLibCheck: true,
        esModuleInterop: false,
        allowSyntheticDefaultImports: true,
        strict: true,
        forceConsistentCasingInFileNames: true,
        module: 'ESNext',
        moduleResolution: 'Node',
        resolveJsonModule: true,
        isolatedModules: true,
        noEmit: true,
        jsx: 'react-jsx'
      },
      include: ['src'],
      references: [{ path: './tsconfig.node.json' }]
    };

    return JSON.stringify(config, null, 2);
  }

  /**
   * Generate Vite configuration
   */
  private generateViteConfig(dependencies: DependencyInfo[], projectContext: ProjectContext): string {
    return `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 5173,
    open: true
  },
  build: {
    outDir: 'dist',
    sourcemap: true
  }
})`;
  }

  /**
   * Generate ESLint configuration
   */
  private generateESLintConfig(dependencies: DependencyInfo[]): string {
    const config = {
      root: true,
      env: { browser: true, es2021: true },
      extends: [
        'eslint:recommended',
        '@typescript-eslint/recommended',
        'plugin:react/recommended',
        'plugin:react-hooks/recommended'
      ],
      ignorePatterns: ['dist', '.eslintrc.cjs'],
      parser: '@typescript-eslint/parser',
      plugins: ['react-refresh'],
      rules: {
        'react-refresh/only-export-components': [
          'warn',
          { allowConstantExport: true }
        ],
        'react/react-in-jsx-scope': 'off'
      }
    };

    return JSON.stringify(config, null, 2);
  }

  /**
   * Generate environment variables
   */
  private generateEnvironmentVariables(dependencies: DependencyInfo[], projectContext: ProjectContext): Record<string, string> {
    const envVars: Record<string, string> = {};

    // Add common environment variables
    envVars.NODE_ENV = 'development';
    envVars.VITE_APP_NAME = projectContext.name;
    envVars.VITE_APP_VERSION = '1.0.0';

    // Add API keys for external services
    if (dependencies.some(dep => dep.name.includes('audio') || dep.purpose.includes('audio'))) {
      envVars.VITE_FREESOUND_API_KEY = 'your_freesound_api_key_here';
      envVars.VITE_HUGGINGFACE_API_KEY = 'your_huggingface_api_key_here';
    }

    if (dependencies.some(dep => dep.purpose.includes('image') || dep.purpose.includes('photo'))) {
      envVars.VITE_UNSPLASH_API_KEY = 'your_unsplash_api_key_here';
      envVars.VITE_PIXABAY_API_KEY = 'your_pixabay_api_key_here';
    }

    return envVars;
  }

  /**
   * Utility methods
   */
  private inferProjectType(projectContext: ProjectContext): string {
    const context = projectContext.fullContext.toLowerCase();
    const idea = projectContext.idea.toLowerCase();
    
    if (context.includes('game') || idea.includes('game')) return 'Game Application';
    if (context.includes('react') || context.includes('spa')) return 'React SPA';
    if (context.includes('node') || context.includes('express')) return 'Node.js Application';
    if (context.includes('mobile') || context.includes('pwa')) return 'Mobile/PWA Application';
    return 'Web Application';
  }

  /**
   * Validation methods
   */
  private validateDependencies(data: any): data is {dependencies: DependencyInfo[]} {
    return (
      typeof data === 'object' &&
      data !== null &&
      'dependencies' in data &&
      Array.isArray(data.dependencies) &&
      data.dependencies.every((dep: any) =>
        typeof dep === 'object' &&
        'name' in dep &&
        'version' in dep &&
        'type' in dep &&
        'purpose' in dep &&
        'required' in dep
      )
    );
  }

  /**
   * System instructions
   */
  private getDependencyAnalysisSystemInstruction(): string {
    return `You are a Dependency Resolver Agent responsible for identifying and configuring all dependencies needed for complete project functionality.

Analyze the project and identify comprehensive dependency requirements including:

1. **Core Dependencies**: Essential libraries and frameworks
2. **Build Tools**: Development and build process tools
3. **Testing**: Testing frameworks and utilities
4. **Styling**: CSS frameworks and preprocessors
5. **Utilities**: Helper libraries and tools
6. **Game/Audio Libraries**: If applicable to project type
7. **Database/Storage**: If data persistence is needed
8. **Authentication**: If user management is required

For each dependency, specify:
- name: Package name
- version: Specific version or range
- type: 'npm' | 'cdn' | 'local' | 'api' | 'service'
- purpose: Why this dependency is needed
- required: true for essential deps, false for optional/dev deps
- installCommand: How to install (if not standard npm)
- configurationRequired: Whether additional setup is needed

Ensure all dependencies are:
- Actively maintained and stable
- Compatible with each other
- Necessary for complete functionality
- Well-documented and reliable

Respond with JSON: {"dependencies": [DependencyInfo array]}`;
  }
}
