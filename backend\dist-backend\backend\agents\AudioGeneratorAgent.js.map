{"version": 3, "file": "AudioGeneratorAgent.js", "sourceRoot": "", "sources": ["../../../agents/AudioGeneratorAgent.ts"], "names": [], "mappings": "AAAA,OAAO,EAIL,SAAS,EACV,MAAM,gBAAgB,CAAC;AAYxB;;;GAGG;AACH,MAAM,OAAO,mBAAmB;IAC9B,YACU,aAAmC,EACnC,gBAAyC;QADzC,kBAAa,GAAb,aAAa,CAAsB;QACnC,qBAAgB,GAAhB,gBAAgB,CAAyB;IAChD,CAAC;IAEJ;;OAEG;IACK,KAAK,CAAC,WAAW,CAAC,OAAe,EAAE,MAAgD;QACzF,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,uBAAuB,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACtF,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,QAAgB,EAAE,SAAiB,EAAE,UAAmB,EAAE,MAAe;QAC3F,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,SAAS,CAAC,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;IAChH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,mBAAmB,CAC9B,cAA8B,EAC9B,iBAA2B,EAC3B,SAAiB,EACjB,MAAe;QAEf,MAAM,IAAI,CAAC,WAAW,CAAC,uDAAuD,EAAE,SAAS,CAAC,CAAC;QAE3F,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,0BAA0B,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;YAElF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAC5D,SAAS,EACT,MAAM,EACN,IAAI,CAAC,oBAAoB,EAAE,EAC3B,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,EAC9C,GAAG,CACJ,CAAC;YAEF,yCAAyC;YACzC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YAC5F,QAAQ,CAAC,WAAW,GAAG,eAAe,CAAC;YAEvC,4BAA4B;YAC5B,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC,4BAA4B,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;YAE9F,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,eAAe,CAAC,MAAM,4BAA4B,EAAE,SAAS,CAAC,CAAC;YACnG,IAAI,CAAC,WAAW,CACd,wBAAwB,EACxB,aAAa,eAAe,CAAC,MAAM,kEAAkE,EACrG,SAAS,EACT,MAAM,CACP,CAAC;YAEF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,CAAC,WAAW,CAAC,kCAAkC,YAAY,EAAE,EAAE,OAAO,CAAC,CAAC;YAClF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,cAA8B,EAAE,YAAsB;QACvF,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;QAE1D,OAAO;mBACQ,cAAc,CAAC,WAAW;gBAC7B,WAAW;sBACL,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;CAwB5C,CAAC;IACA,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,MAAoB,EAAE,cAA8B;QACnF,MAAM,eAAe,GAAiB,EAAE,CAAC;QAEzC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,CAAC;gBACH,IAAI,aAAyB,CAAC;gBAE9B,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;oBACnB,KAAK,OAAO;wBACV,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;wBAChD,MAAM;oBACR,KAAK,KAAK;wBACR,aAAa,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;wBAClD,MAAM;oBACR,KAAK,SAAS;wBACZ,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;wBACnD,MAAM;oBACR,KAAK,OAAO;wBACV,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;wBAChD,MAAM;oBACR;wBACE,aAAa,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;gBAC7D,CAAC;gBAED,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACpC,MAAM,IAAI,CAAC,WAAW,CAAC,yBAAyB,KAAK,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC;YACxE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,IAAI,CAAC,WAAW,CAAC,qBAAqB,KAAK,CAAC,IAAI,qBAAqB,EAAE,OAAO,CAAC,CAAC;gBACtF,eAAe,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,KAAiB;QAC3C,2DAA2D;QAC3D,sDAAsD;QACtD,OAAO;YACL,GAAG,KAAK;YACR,GAAG,EAAE,uCAAuC,KAAK,CAAC,EAAE,MAAM;YAC1D,SAAS,EAAE,sBAAsB,KAAK,CAAC,EAAE,MAAM;YAC/C,OAAO,EAAE,WAAW;YACpB,WAAW,EAAE,oBAAoB;SAClC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,KAAiB;QAC7C,+CAA+C;QAC/C,OAAO;YACL,GAAG,KAAK;YACR,GAAG,EAAE,uCAAuC,KAAK,CAAC,EAAE,MAAM;YAC1D,SAAS,EAAE,oBAAoB,KAAK,CAAC,EAAE,MAAM;YAC7C,OAAO,EAAE,WAAW;YACpB,WAAW,EAAE,eAAe;SAC7B,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,KAAiB;QAC9C,OAAO;YACL,GAAG,KAAK;YACR,GAAG,EAAE,+BAA+B,KAAK,CAAC,EAAE,MAAM;YAClD,SAAS,EAAE,wBAAwB,KAAK,CAAC,EAAE,MAAM;YACjD,OAAO,EAAE,KAAK;YACd,WAAW,EAAE,eAAe;SAC7B,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,KAAiB;QAC3C,OAAO;YACL,GAAG,KAAK;YACR,GAAG,EAAE,6BAA6B,KAAK,CAAC,EAAE,MAAM;YAChD,SAAS,EAAE,sBAAsB,KAAK,CAAC,EAAE,MAAM;YAC/C,OAAO,EAAE,WAAW;YACpB,WAAW,EAAE,oBAAoB;SAClC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,KAAiB;QACpD,OAAO;YACL,GAAG,KAAK;YACR,GAAG,EAAE,yQAAyQ;YAC9Q,SAAS,EAAE,4BAA4B,KAAK,CAAC,EAAE,MAAM;YACrD,OAAO,EAAE,aAAa;YACtB,WAAW,EAAE,uBAAuB;SACrC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,4BAA4B,CAAC,MAAoB,EAAE,cAA8B;QACvF,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;QAE1D,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACjE,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAC3C,CAAC;aAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,MAAoB;QAC/C,OAAO;;;;;;;;;;;0BAWe,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2CxD,CAAC;IACA,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,MAAoB;QAChD,OAAO;;;;;;;;;;;;;;;;;;0BAkBe,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqCxD,CAAC;IACA,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,MAAoB;QACnD,OAAO;;sBAEW,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;CAoBpD,CAAC;IACA,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,cAA8B;QACrD,MAAM,OAAO,GAAG,cAAc,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;QACzD,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;QAE/C,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,MAAM,CAAC;QACrE,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,eAAe,CAAC;QACrF,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,YAAY,CAAC;QACpD,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,8BAA8B,CAAC,IAAS;QAC9C,OAAO,CACL,OAAO,IAAI,KAAK,QAAQ;YACxB,IAAI,KAAK,IAAI;YACb,aAAa,IAAI,IAAI;YACrB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;YAC/B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,KAAU,EAAE,EAAE,CACpC,OAAO,KAAK,KAAK,QAAQ;gBACzB,IAAI,IAAI,KAAK;gBACb,MAAM,IAAI,KAAK;gBACf,MAAM,IAAI,KAAK;gBACf,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAC1D,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,OAAO;;;;;;;;;;;;;;;;;;;;;;;uFAuB4E,CAAC;IACtF,CAAC;CACF"}