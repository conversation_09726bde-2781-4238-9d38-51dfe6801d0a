import React, { useState } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Loader2, Sparkles, Zap, Shield, Rocket } from 'lucide-react';

interface UserAuthorshipDetails {
  name: string;
  email: string;
  license: string;
}

interface AutonomousGenerationProps {
  onProjectGenerated?: (projectId: string) => void;
}

export const AutonomousGeneration: React.FC<AutonomousGenerationProps> = ({ onProjectGenerated }) => {
  const [idea, setIdea] = useState('');
  const [userDetails, setUserDetails] = useState<UserAuthorshipDetails>({
    name: '',
    email: '',
    license: 'MIT'
  });
  const [apiKey, setApiKey] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationStatus, setGenerationStatus] = useState<string>('');

  const handleGenerate = async () => {
    if (!idea.trim() || !userDetails.name.trim() || !userDetails.email.trim() || !apiKey.trim()) {
      alert('Please fill in all required fields');
      return;
    }

    setIsGenerating(true);
    setGenerationStatus('Initializing autonomous generation...');

    try {
      const response = await fetch('/api/generation/autonomous', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          idea,
          userAuthorshipDetails: userDetails,
          apiKey
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to start autonomous generation');
      }

      const result = await response.json();
      setGenerationStatus('Autonomous generation started successfully!');
      
      if (onProjectGenerated) {
        onProjectGenerated(result.projectId || 'autonomous-project');
      }
    } catch (error) {
      console.error('Error starting autonomous generation:', error);
      setGenerationStatus(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsGenerating(false);
    }
  };

  const exampleIdeas = [
    "Create a 2D RPG tower-climbing game with monster collection and breeding mechanics",
    "Build a modern e-commerce website with payment integration and inventory management",
    "Develop a real-time chat application with video calling capabilities",
    "Create a personal finance dashboard with expense tracking and budget planning",
    "Build a recipe sharing platform with meal planning and shopping lists"
  ];

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-6 w-6 text-purple-500" />
            Autonomous Project Generation
          </CardTitle>
          <CardDescription>
            Generate complete, deployment-ready projects with 100% autonomy using specialized AI agents
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Capabilities Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
              <Zap className="h-5 w-5 text-blue-500" />
              <div>
                <div className="font-semibold text-sm">Audio Generation</div>
                <div className="text-xs text-gray-600">Music & SFX</div>
              </div>
            </div>
            <div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg">
              <Shield className="h-5 w-5 text-green-500" />
              <div>
                <div className="font-semibold text-sm">Asset Management</div>
                <div className="text-xs text-gray-600">Images & Fonts</div>
              </div>
            </div>
            <div className="flex items-center gap-2 p-3 bg-purple-50 rounded-lg">
              <Rocket className="h-5 w-5 text-purple-500" />
              <div>
                <div className="font-semibold text-sm">Auto Deployment</div>
                <div className="text-xs text-gray-600">Multi-platform</div>
              </div>
            </div>
            <div className="flex items-center gap-2 p-3 bg-orange-50 rounded-lg">
              <Sparkles className="h-5 w-5 text-orange-500" />
              <div>
                <div className="font-semibold text-sm">Quality Assurance</div>
                <div className="text-xs text-gray-600">100% Ready</div>
              </div>
            </div>
          </div>

          {/* Project Idea Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Project Idea *</label>
            <Textarea
              placeholder="Describe your project idea in detail..."
              value={idea}
              onChange={(e) => setIdea(e.target.value)}
              className="min-h-[100px]"
            />
            <div className="text-xs text-gray-500">
              Be as specific as possible. The AI will generate everything needed for your project.
            </div>
          </div>

          {/* Example Ideas */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Example Ideas</label>
            <div className="flex flex-wrap gap-2">
              {exampleIdeas.map((example, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="cursor-pointer hover:bg-gray-100 text-xs p-2"
                  onClick={() => setIdea(example)}
                >
                  {example.substring(0, 50)}...
                </Badge>
              ))}
            </div>
          </div>

          {/* User Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Your Name *</label>
              <Input
                placeholder="Developer Name"
                value={userDetails.name}
                onChange={(e) => setUserDetails(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Email *</label>
              <Input
                type="email"
                placeholder="<EMAIL>"
                value={userDetails.email}
                onChange={(e) => setUserDetails(prev => ({ ...prev, email: e.target.value }))}
              />
            </div>
          </div>

          {/* License Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">License</label>
            <select
              className="w-full p-2 border rounded-md"
              value={userDetails.license}
              onChange={(e) => setUserDetails(prev => ({ ...prev, license: e.target.value }))}
            >
              <option value="MIT">MIT License</option>
              <option value="Apache-2.0">Apache 2.0</option>
              <option value="GPL-3.0">GPL 3.0</option>
              <option value="BSD-3-Clause">BSD 3-Clause</option>
              <option value="Proprietary">Proprietary</option>
            </select>
          </div>

          {/* API Key */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Gemini API Key *</label>
            <Input
              type="password"
              placeholder="Enter your Gemini API key"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
            />
            <div className="text-xs text-gray-500">
              Your API key is used securely and not stored. Get one from{' '}
              <a href="https://makersuite.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                Google AI Studio
              </a>
            </div>
          </div>

          {/* Generation Status */}
          {generationStatus && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="text-sm text-blue-800">{generationStatus}</div>
            </div>
          )}

          {/* Generate Button */}
          <Button
            onClick={handleGenerate}
            disabled={isGenerating || !idea.trim() || !userDetails.name.trim() || !userDetails.email.trim() || !apiKey.trim()}
            className="w-full"
            size="lg"
          >
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating Autonomous Project...
              </>
            ) : (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                Generate Complete Project
              </>
            )}
          </Button>

          {/* Features List */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-semibold mb-2">What You'll Get:</h3>
            <ul className="text-sm space-y-1 text-gray-600">
              <li>✅ Complete source code with all features implemented</li>
              <li>✅ All required assets (images, fonts, icons) automatically sourced</li>
              <li>✅ Audio assets (music, sound effects) generated and integrated</li>
              <li>✅ Deployment configurations for multiple platforms</li>
              <li>✅ Quality assurance testing and optimization</li>
              <li>✅ Documentation and setup instructions</li>
              <li>✅ Ready-to-deploy project with no manual intervention needed</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
