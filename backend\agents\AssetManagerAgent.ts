import {
  GeminiJsonAssetManagerResponse,
  AssetRequirement,
  ManagedAsset,
  ProjectContext,
  AgentType
} from '../../types.js';
import { BackendGeminiService } from '../services/BackendGeminiService.js';

/**
 * Interface for backend logging that sends updates to frontend
 */
interface BackendLoggingInterface {
  addCompanyLog: (agent: string, message: string, status: 'info' | 'success' | 'error' | 'working', taskId?: string) => Promise<void>;
  addTaskLog: (taskId: string, agent: string, message: string, status: 'info' | 'success' | 'error' | 'working', stage?: any, subDetailSections?: Array<{title: string, content: string, isCodeBlock?: boolean}>) => Promise<void>;
  addDecisionLogEntry: (agent: string, decision: string, reasoning: string, stackTrace?: string, taskId?: string) => Promise<void>;
}

/**
 * Asset Manager Agent - Handles autonomous asset acquisition and management
 * Integrates with free asset APIs and repositories
 */
export class AssetManagerAgent {
  constructor(
    private geminiService: BackendGeminiService,
    private loggingInterface: BackendLoggingInterface
  ) {}

  /**
   * Log activity to the frontend
   */
  private async logActivity(message: string, status: 'info' | 'success' | 'error' | 'working'): Promise<void> {
    await this.loggingInterface.addCompanyLog('Asset Manager Agent', message, status);
  }

  /**
   * Log decision to the frontend
   */
  private logDecision(decision: string, reasoning: string, stackTrace?: string, taskId?: string): void {
    this.loggingInterface.addDecisionLogEntry(AgentType.ASSET_MANAGER, decision, reasoning, stackTrace, taskId);
  }

  /**
   * Analyze project and acquire all required assets
   */
  public async manageProjectAssets(
    projectContext: ProjectContext,
    modelName: string,
    taskId?: string
  ): Promise<GeminiJsonAssetManagerResponse> {
    await this.logActivity('Analyzing project asset requirements...', 'working');
    
    try {
      // First, analyze what assets are needed
      const assetRequirements = await this.analyzeAssetRequirements(projectContext, modelName);
      
      // Then acquire each asset
      const managedAssets = await this.acquireAssets(assetRequirements, projectContext);
      
      // Generate integration instructions
      const integrationInstructions = this.generateIntegrationInstructions(managedAssets, projectContext);
      
      const response: GeminiJsonAssetManagerResponse = {
        assets: managedAssets,
        integrationInstructions,
        assetManifest: this.generateAssetManifest(managedAssets)
      };

      await this.logActivity(`Successfully managed ${managedAssets.length} project assets`, 'success');
      this.logDecision(
        'Project Assets Managed',
        `Acquired and integrated ${managedAssets.length} assets including images, fonts, and icons`,
        undefined,
        taskId
      );

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Error managing assets: ${errorMessage}`, 'error');
      throw error;
    }
  }

  /**
   * Analyze what assets the project needs
   */
  private async analyzeAssetRequirements(
    projectContext: ProjectContext,
    modelName: string
  ): Promise<AssetRequirement[]> {
    const prompt = this.buildAssetAnalysisPrompt(projectContext);
    
    const response = await this.geminiService.makeRequestWithRetry<{requirements: AssetRequirement[]}>(
      modelName,
      prompt,
      this.getAssetAnalysisSystemInstruction(),
      (data: any) => this.validateAssetRequirements(data),
      0.5
    );

    return response.requirements;
  }

  /**
   * Build prompt for asset analysis
   */
  private buildAssetAnalysisPrompt(projectContext: ProjectContext): string {
    return `
Project Context: ${projectContext.fullContext}
Project Idea: ${projectContext.idea}
File Structure: ${JSON.stringify(projectContext.fileStructure, null, 2)}

Analyze this project and identify ALL assets that will be needed for a complete, professional implementation:

1. **Images**: Logos, backgrounds, UI elements, icons, illustrations
2. **Fonts**: Typography for headers, body text, special elements
3. **Icons**: Navigation, actions, status indicators, social media
4. **Data**: Sample data, configuration files, templates

For each asset requirement, specify:
- Type and category
- Detailed description
- Technical specifications (dimensions, format, style)
- Priority level (critical/high/medium/low)
- Usage context within the project

Consider the project type and ensure all visual and data assets needed for a complete, polished application are identified.
`;
  }

  /**
   * Acquire assets from various sources
   */
  private async acquireAssets(
    requirements: AssetRequirement[],
    projectContext: ProjectContext
  ): Promise<ManagedAsset[]> {
    const managedAssets: ManagedAsset[] = [];

    for (const requirement of requirements) {
      try {
        let asset: ManagedAsset;

        switch (requirement.type) {
          case 'image':
            asset = await this.acquireImage(requirement);
            break;
          case 'font':
            asset = await this.acquireFont(requirement);
            break;
          case 'icon':
            asset = await this.acquireIcon(requirement);
            break;
          case 'data':
            asset = await this.generateDataAsset(requirement);
            break;
          default:
            asset = await this.createPlaceholderAsset(requirement);
        }

        managedAssets.push(asset);
        await this.logActivity(`Acquired asset: ${requirement.description}`, 'info');
      } catch (error) {
        await this.logActivity(`Failed to acquire ${requirement.description}, using placeholder`, 'error');
        managedAssets.push(await this.createPlaceholderAsset(requirement));
      }
    }

    return managedAssets;
  }

  /**
   * Acquire image assets
   */
  private async acquireImage(requirement: AssetRequirement): Promise<ManagedAsset> {
    // For now, create structured placeholder that could be replaced with actual API calls
    const imageId = this.generateAssetId(requirement);
    
    return {
      id: imageId,
      requirement,
      source: 'api',
      url: `https://picsum.photos/800/600?random=${imageId}`, // Placeholder service
      localPath: `assets/images/${requirement.category}/${imageId}.jpg`,
      metadata: {
        dimensions: requirement.specifications?.dimensions || '800x600',
        license: 'Lorem Picsum License',
        attribution: 'Lorem Picsum'
      },
      status: 'acquired'
    };
  }

  /**
   * Acquire font assets
   */
  private async acquireFont(requirement: AssetRequirement): Promise<ManagedAsset> {
    const fontId = this.generateAssetId(requirement);
    
    // Use Google Fonts as primary source
    const fontName = this.suggestFont(requirement);
    
    return {
      id: fontId,
      requirement,
      source: 'api',
      url: `https://fonts.googleapis.com/css2?family=${fontName.replace(' ', '+')}:wght@300;400;500;700&display=swap`,
      localPath: `assets/fonts/${fontName.toLowerCase().replace(' ', '-')}.css`,
      metadata: {
        license: 'Open Font License',
        attribution: 'Google Fonts'
      },
      status: 'acquired'
    };
  }

  /**
   * Acquire icon assets
   */
  private async acquireIcon(requirement: AssetRequirement): Promise<ManagedAsset> {
    const iconId = this.generateAssetId(requirement);
    
    return {
      id: iconId,
      requirement,
      source: 'api',
      url: `https://api.iconify.design/mdi/${this.suggestIconName(requirement)}.svg`,
      localPath: `assets/icons/${iconId}.svg`,
      metadata: {
        license: 'Apache 2.0',
        attribution: 'Material Design Icons'
      },
      status: 'acquired'
    };
  }

  /**
   * Generate data assets
   */
  private async generateDataAsset(requirement: AssetRequirement): Promise<ManagedAsset> {
    const dataId = this.generateAssetId(requirement);
    
    return {
      id: dataId,
      requirement,
      source: 'generated',
      localPath: `assets/data/${dataId}.json`,
      metadata: {
        license: 'Generated',
        attribution: 'AI Generated Data'
      },
      status: 'acquired'
    };
  }

  /**
   * Create placeholder asset
   */
  private async createPlaceholderAsset(requirement: AssetRequirement): Promise<ManagedAsset> {
    const placeholderId = this.generateAssetId(requirement);
    
    return {
      id: placeholderId,
      requirement,
      source: 'placeholder',
      localPath: `assets/placeholders/${placeholderId}.${this.getDefaultFormat(requirement.type)}`,
      metadata: {
        license: 'Placeholder',
        attribution: 'Generated Placeholder'
      },
      status: 'acquired'
    };
  }

  /**
   * Generate integration instructions
   */
  private generateIntegrationInstructions(assets: ManagedAsset[], projectContext: ProjectContext): string[] {
    const instructions: string[] = [];
    
    // Group assets by type
    const assetsByType = assets.reduce((acc, asset) => {
      if (!acc[asset.requirement.type]) acc[asset.requirement.type] = [];
      acc[asset.requirement.type].push(asset);
      return acc;
    }, {} as Record<string, ManagedAsset[]>);

    // Generate type-specific instructions
    if (assetsByType.image) {
      instructions.push('Import images in components using: import imageName from "./assets/images/category/image.jpg"');
    }
    
    if (assetsByType.font) {
      instructions.push('Add font imports to your main CSS file or index.html');
      instructions.push('Use fonts in CSS with: font-family: "FontName", sans-serif');
    }
    
    if (assetsByType.icon) {
      instructions.push('Icons can be used as SVG imports or through icon libraries');
    }

    return instructions;
  }

  /**
   * Generate asset manifest
   */
  private generateAssetManifest(assets: ManagedAsset[]) {
    const byType = assets.reduce((acc, asset) => {
      acc[asset.requirement.type] = (acc[asset.requirement.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalAssets: assets.length,
      byType,
      totalSize: assets.length * 50000 // Estimated average size
    };
  }

  /**
   * Utility methods
   */
  private generateAssetId(requirement: AssetRequirement): string {
    return `${requirement.type}_${requirement.category}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private suggestFont(requirement: AssetRequirement): string {
    const fonts = ['Inter', 'Roboto', 'Open Sans', 'Lato', 'Montserrat', 'Source Sans Pro'];
    return fonts[Math.floor(Math.random() * fonts.length)];
  }

  private suggestIconName(requirement: AssetRequirement): string {
    const commonIcons = ['home', 'user', 'settings', 'search', 'menu', 'close', 'check', 'arrow-right'];
    return commonIcons[Math.floor(Math.random() * commonIcons.length)];
  }

  private getDefaultFormat(type: string): string {
    switch (type) {
      case 'image': return 'jpg';
      case 'icon': return 'svg';
      case 'font': return 'woff2';
      case 'data': return 'json';
      default: return 'txt';
    }
  }

  /**
   * Validation methods
   */
  private validateAssetRequirements(data: any): data is {requirements: AssetRequirement[]} {
    return (
      typeof data === 'object' &&
      data !== null &&
      'requirements' in data &&
      Array.isArray(data.requirements) &&
      data.requirements.every((req: any) =>
        typeof req === 'object' &&
        'type' in req &&
        'category' in req &&
        'description' in req &&
        'priority' in req
      )
    );
  }

  /**
   * System instructions
   */
  private getAssetAnalysisSystemInstruction(): string {
    return `You are an Asset Manager Agent responsible for identifying all assets needed for a complete project implementation.

Analyze the project and identify comprehensive asset requirements including:

1. **Images**: All visual content needed (logos, backgrounds, UI elements, illustrations)
2. **Fonts**: Typography requirements for different text elements
3. **Icons**: All iconography needed for navigation, actions, and UI
4. **Data**: Sample data, configuration files, templates

For each asset requirement, specify:
- type: 'image' | 'font' | 'icon' | 'data'
- category: Specific category within the type
- description: Detailed description of the asset
- specifications: Technical requirements (dimensions, format, style)
- priority: 'critical' | 'high' | 'medium' | 'low'

Ensure comprehensive coverage - identify ALL assets needed for a polished, professional application.

Respond with JSON: {"requirements": [AssetRequirement array]}`;
  }
}
