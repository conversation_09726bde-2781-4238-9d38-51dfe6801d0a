import { io, Socket } from 'socket.io-client';
import type {
  Task,
  AgentLog,
  DecisionLogEntry,
  LicenseInfo,
  AgentType,
  UserFeedback,
  FileNode,
  ProjectContext,
  GeminiJsonPlannerResponse,
  GeminiJsonPlannerTask,
  GeminiJsonCoderResponse,
  GeminiJsonBugHunterResponse,
  GeminiJsonSecurityAnalystResponse,
  GeminiJsonRefactorerResponse,
  GeminiJsonContextManagerResponse,
  GeminiJsonLinterFormatterResponse,
  GeminiJsonTesterResponse,
  GeminiJsonImageGeneratorResponse,
  GeminiJsonBuildValidationResponse
} from '../../types';

/**
 * Service that communicates with the backend for project generation
 * This replaces the frontend agents that were causing memory issues
 */
export class BackendProjectGenerationService {
  private socket: Socket | null = null;
  private baseUrl: string;
  private isInitialized = false;

  // Event handlers
  private onCompanyLogHandler?: (log: AgentLog) => void;
  private onTaskLogHandler?: (log: any) => void;
  private onDecisionLogHandler?: (entry: DecisionLogEntry) => void;
  private onProjectGenerationUpdateHandler?: (update: any) => void;
  private onProjectGenerationErrorHandler?: (error: any) => void;

  constructor(baseUrl: string = 'http://localhost:3002') {
    this.baseUrl = baseUrl;
  }

  /**
   * Initialize the service and connect to WebSocket
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    // Connect to WebSocket
    this.socket = io(this.baseUrl);

    this.socket.on('connect', () => {
      console.log('[BackendProjectGenerationService] Connected to backend WebSocket');
    });

    this.socket.on('disconnect', () => {
      console.log('[BackendProjectGenerationService] Disconnected from backend WebSocket');
    });

    this.socket.on('company-log', (log: AgentLog) => {
      if (this.onCompanyLogHandler) {
        this.onCompanyLogHandler(log);
      }
    });

    this.socket.on('task-log', (log: any) => {
      if (this.onTaskLogHandler) {
        this.onTaskLogHandler(log);
      }
    });

    this.socket.on('decision-log', (entry: DecisionLogEntry) => {
      if (this.onDecisionLogHandler) {
        this.onDecisionLogHandler(entry);
      }
    });

    this.socket.on('project-generation-update', (update: any) => {
      if (this.onProjectGenerationUpdateHandler) {
        this.onProjectGenerationUpdateHandler(update);
      }
    });

    this.socket.on('project-generation-error', (error: any) => {
      if (this.onProjectGenerationErrorHandler) {
        this.onProjectGenerationErrorHandler(error);
      }
    });

    this.isInitialized = true;
  }

  /**
   * Disconnect from the backend
   */
  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isInitialized = false;
  }

  /**
   * Initialize the backend service with API key
   */
  public async initializeBackendWithApiKey(apiKey: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/api/generation/initialize`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ apiKey }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to initialize backend service');
    }

    console.log('[BackendProjectGenerationService] Backend initialized with API key');
  }

  /**
   * Start project generation on the backend
   */
  public async startProjectGeneration(
    projectId: string,
    projectIdea: string,
    licenseInfo: LicenseInfo,
    agentModelConfiguration: Record<AgentType, string>
  ): Promise<void> {
    // Join the project room for real-time updates
    if (this.socket) {
      this.socket.emit('join-project', projectId);
    }

    const response = await fetch(`${this.baseUrl}/api/generation/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectId,
        projectIdea,
        licenseInfo,
        agentModelConfiguration,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to start project generation');
    }

    console.log('[BackendProjectGenerationService] Project generation started');
  }

  /**
   * Get project generation status
   */
  public async getGenerationStatus(): Promise<{ isGenerating: boolean; projectId: string | null }> {
    const response = await fetch(`${this.baseUrl}/api/generation/status`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to get generation status');
    }

    return response.json();
  }

  /**
   * Process user feedback and generate additional tasks
   */
  public async processUserFeedback(
    projectId: string,
    projectContext: string,
    projectIdea: string,
    fileStructure: FileNode[],
    feedback: UserFeedback,
    modelName: string
  ): Promise<Task[]> {
    const response = await fetch(`${this.baseUrl}/api/generation/feedback`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectId,
        projectContext,
        projectIdea,
        fileStructure,
        feedback,
        modelName,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to process user feedback');
    }

    const result = await response.json();
    return result.tasks;
  }

  /**
   * Set event handlers for real-time updates
   */
  public setEventHandlers(handlers: {
    onCompanyLog?: (log: AgentLog) => void;
    onTaskLog?: (log: any) => void;
    onDecisionLog?: (entry: DecisionLogEntry) => void;
    onProjectGenerationUpdate?: (update: any) => void;
    onProjectGenerationError?: (error: any) => void;
  }): void {
    this.onCompanyLogHandler = handlers.onCompanyLog;
    this.onTaskLogHandler = handlers.onTaskLog;
    this.onDecisionLogHandler = handlers.onDecisionLog;
    this.onProjectGenerationUpdateHandler = handlers.onProjectGenerationUpdate;
    this.onProjectGenerationErrorHandler = handlers.onProjectGenerationError;
  }

  /**
   * Check if the service is initialized
   */
  public get initialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Check if WebSocket is connected
   */
  public get connected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * Clarify a question using the backend clarifier agent
   */
  public async clarifyQuestion(
    question: string,
    projectContext: ProjectContext,
    modelName: string
  ): Promise<{ answer: string }> {
    const response = await fetch(`${this.baseUrl}/api/generation/clarify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        question,
        projectContext,
        modelName,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to clarify question');
    }

    return response.json();
  }

  /**
   * Review and refine a project plan using the backend planner agent
   */
  public async reviewAndRefinePlan(
    projectIdea: string,
    initialTasks: GeminiJsonPlannerTask[],
    initialFileStructure: any[],
    projectContext: string,
    modelName: string,
    technologyStackSuggestion?: string
  ): Promise<GeminiJsonPlannerResponse> {
    const response = await fetch(`${this.baseUrl}/api/generation/review-plan`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectIdea,
        initialTasks,
        initialFileStructure,
        projectContext,
        modelName,
        technologyStackSuggestion,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to review and refine plan');
    }

    return response.json();
  }

  /**
   * Generate tasks from user feedback using the backend planner agent
   */
  public async generateTasksFromFeedback(
    projectContext: string,
    projectIdea: string,
    fileStructure: FileNode[],
    feedback: UserFeedback,
    modelName: string
  ): Promise<GeminiJsonPlannerResponse> {
    const response = await fetch(`${this.baseUrl}/api/generation/tasks-from-feedback`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectContext,
        projectIdea,
        fileStructure,
        feedback,
        modelName,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to generate tasks from feedback');
    }

    return response.json();
  }

  /**
   * Generate image using the backend image generator agent
   */
  public async generateImage(
    prompt: string,
    modelName: string
  ): Promise<GeminiJsonImageGeneratorResponse> {
    const response = await fetch(`${this.baseUrl}/api/generation/generate-image`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt,
        modelName,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to generate image');
    }

    return response.json();
  }

  /**
   * Generate file content using the backend coder agent
   */
  public async generateFileContent(
    projectIdea: string,
    filePath: string,
    taskDescription: string,
    projectContext: string,
    fileStructure: FileNode[],
    modelName: string,
    licenseInfo?: LicenseInfo,
    clarifierResponse?: string
  ): Promise<GeminiJsonCoderResponse> {
    const response = await fetch(`${this.baseUrl}/api/generation/generate-file-content`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectIdea,
        filePath,
        taskDescription,
        projectContext,
        fileStructure,
        modelName,
        licenseInfo,
        clarifierResponse,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to generate file content');
    }

    return response.json();
  }

  /**
   * Generate test code using the backend tester agent
   */
  public async generateTestCode(
    projectContext: string,
    testFilePath: string,
    testDescription: string,
    relatedSourceFiles: string[],
    fileStructure: FileNode[],
    modelName: string
  ): Promise<GeminiJsonCoderResponse> {
    const response = await fetch(`${this.baseUrl}/api/generation/generate-test-code`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectContext,
        testFilePath,
        testDescription,
        relatedSourceFiles,
        fileStructure,
        modelName,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to generate test code');
    }

    return response.json();
  }

  /**
   * Lint and format code using the backend linter formatter agent
   */
  public async lintAndFormatCode(
    code: string,
    filePath: string,
    projectContext: string,
    modelName: string
  ): Promise<GeminiJsonLinterFormatterResponse> {
    const response = await fetch(`${this.baseUrl}/api/generation/lint-format-code`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        code,
        filePath,
        projectContext,
        modelName,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to lint and format code');
    }

    return response.json();
  }

  /**
   * Analyze code for security vulnerabilities using the backend security analyst agent
   */
  public async analyzeCodeForSecurityVulnerabilities(
    code: string,
    filePath: string,
    projectContext: string,
    fileStructure: FileNode[],
    modelName: string
  ): Promise<GeminiJsonSecurityAnalystResponse> {
    const response = await fetch(`${this.baseUrl}/api/generation/analyze-security`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        code,
        filePath,
        projectContext,
        fileStructure,
        modelName,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to analyze code for security vulnerabilities');
    }

    return response.json();
  }

  /**
   * Analyze code for bugs using the backend bug hunter agent
   */
  public async analyzeCodeForBugs(
    code: string,
    filePath: string,
    projectContext: string,
    fileStructure: FileNode[],
    modelName: string
  ): Promise<GeminiJsonBugHunterResponse> {
    const response = await fetch(`${this.baseUrl}/api/generation/analyze-bugs`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        code,
        filePath,
        projectContext,
        fileStructure,
        modelName,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to analyze code for bugs');
    }

    return response.json();
  }

  /**
   * Refactor code using the backend refactorer agent
   */
  public async refactorCode(
    code: string,
    bugDescription: string,
    bugId: string,
    filePath: string,
    projectContext: string,
    fileStructure: FileNode[],
    modelName: string,
    isSecurityIssue: boolean = false
  ): Promise<GeminiJsonRefactorerResponse> {
    const response = await fetch(`${this.baseUrl}/api/generation/refactor-code`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        code,
        bugDescription,
        bugId,
        filePath,
        projectContext,
        fileStructure,
        modelName,
        isSecurityIssue,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to refactor code');
    }

    return response.json();
  }

  /**
   * Update project context using the backend context manager agent
   */
  public async updateProjectContext(
    currentContext: string,
    newInformation: string,
    modelName: string
  ): Promise<GeminiJsonContextManagerResponse> {
    const response = await fetch(`${this.baseUrl}/api/generation/update-context`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        currentContext,
        newInformation,
        modelName,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to update project context');
    }

    return response.json();
  }

  /**
   * Generate test plan using the backend tester agent
   */
  public async generateTestPlan(
    projectContext: string,
    fileStructure: FileNode[],
    modelName: string
  ): Promise<GeminiJsonTesterResponse> {
    const response = await fetch(`${this.baseUrl}/api/generation/generate-test-plan`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectContext,
        fileStructure,
        modelName,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to generate test plan');
    }

    return response.json();
  }

  /**
   * Analyze project with tests using the backend tester agent
   */
  public async analyzeProjectWithTests(
    projectContext: string,
    fileStructure: FileNode[],
    modelName: string
  ): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/generation/analyze-project-tests`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectContext,
        fileStructure,
        modelName,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to analyze project with tests');
    }

    return response.json();
  }

  /**
   * Analyze single test failure using the backend tester agent
   */
  public async analyzeSingleTestFailure(
    issue: any,
    testFileNode: FileNode,
    fileStructure: FileNode[],
    projectContext: string,
    modelName: string
  ): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/generation/analyze-test-failure`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        issue,
        testFileNode,
        fileStructure,
        projectContext,
        modelName,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to analyze test failure');
    }

    return response.json();
  }

  /**
   * Analyze test coverage using the backend tester agent
   */
  public async analyzeTestCoverage(
    projectContext: string,
    fileStructure: FileNode[],
    modelName: string
  ): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/generation/analyze-test-coverage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectContext,
        fileStructure,
        modelName,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to analyze test coverage');
    }

    return response.json();
  }

  /**
   * Validate project build using the backend planner agent
   */
  public async validateProjectBuild(
    projectContext: string,
    fileStructure: FileNode[],
    packageJsonContent: string | undefined,
    modelName: string
  ): Promise<GeminiJsonBuildValidationResponse> {
    const response = await fetch(`${this.baseUrl}/api/generation/validate-build`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectContext,
        fileStructure,
        packageJsonContent,
        modelName,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to validate project build');
    }

    return response.json();
  }
}
