import {
  GeminiJsonDeploymentManagerResponse,
  DeploymentTarget,
  DeploymentResult,
  ProjectContext,
  AgentType
} from '../../types.js';
import { BackendGeminiService } from '../services/BackendGeminiService.js';

/**
 * Interface for backend logging that sends updates to frontend
 */
interface BackendLoggingInterface {
  addCompanyLog: (agent: string, message: string, status: 'info' | 'success' | 'error' | 'working', taskId?: string) => Promise<void>;
  addTaskLog: (taskId: string, agent: string, message: string, status: 'info' | 'success' | 'error' | 'working', stage?: any, subDetailSections?: Array<{title: string, content: string, isCodeBlock?: boolean}>) => Promise<void>;
  addDecisionLogEntry: (agent: string, decision: string, reasoning: string, stackTrace?: string, taskId?: string) => Promise<void>;
}

/**
 * Deployment Manager Agent - Handles autonomous project deployment
 * Integrates with free hosting platforms and deployment services
 */
export class DeploymentManagerAgent {
  constructor(
    private geminiService: BackendGeminiService,
    private loggingInterface: BackendLoggingInterface
  ) {}

  /**
   * Log activity to the frontend
   */
  private async logActivity(message: string, status: 'info' | 'success' | 'error' | 'working'): Promise<void> {
    await this.loggingInterface.addCompanyLog('Deployment Manager Agent', message, status);
  }

  /**
   * Log decision to the frontend
   */
  private logDecision(decision: string, reasoning: string, stackTrace?: string, taskId?: string): void {
    this.loggingInterface.addDecisionLogEntry(AgentType.DEPLOYMENT_MANAGER, decision, reasoning, stackTrace, taskId);
  }

  /**
   * Deploy project to appropriate platforms
   */
  public async deployProject(
    projectContext: ProjectContext,
    modelName: string,
    taskId?: string
  ): Promise<GeminiJsonDeploymentManagerResponse> {
    await this.logActivity('Analyzing project for deployment...', 'working');
    
    try {
      // Analyze project type and determine deployment targets
      const deploymentTargets = await this.analyzeDeploymentTargets(projectContext, modelName);
      
      // Generate deployment configurations
      const deploymentResults = await this.generateDeploymentConfigurations(deploymentTargets, projectContext);
      
      // Generate build commands and instructions
      const buildCommands = this.generateBuildCommands(projectContext);
      const deploymentInstructions = this.generateDeploymentInstructions(deploymentResults, projectContext);
      const accessUrls = this.generateAccessUrls(deploymentResults);

      const response: GeminiJsonDeploymentManagerResponse = {
        deployments: deploymentResults,
        buildCommands,
        deploymentInstructions,
        accessUrls
      };

      await this.logActivity(`Generated deployment configurations for ${deploymentResults.length} platforms`, 'success');
      this.logDecision(
        'Deployment Configurations Generated',
        `Created deployment setups for ${deploymentResults.length} platforms including build scripts and hosting configurations`,
        undefined,
        taskId
      );

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Error generating deployment configurations: ${errorMessage}`, 'error');
      throw error;
    }
  }

  /**
   * Analyze project and determine appropriate deployment targets
   */
  private async analyzeDeploymentTargets(
    projectContext: ProjectContext,
    modelName: string
  ): Promise<DeploymentTarget[]> {
    const prompt = this.buildDeploymentAnalysisPrompt(projectContext);
    
    const response = await this.geminiService.makeRequestWithRetry<{targets: DeploymentTarget[]}>(
      modelName,
      prompt,
      this.getDeploymentAnalysisSystemInstruction(),
      (data: any) => this.validateDeploymentTargets(data),
      0.5
    );

    return response.targets;
  }

  /**
   * Build prompt for deployment analysis
   */
  private buildDeploymentAnalysisPrompt(projectContext: ProjectContext): string {
    const projectType = this.inferProjectType(projectContext);
    
    return `
Project Context: ${projectContext.fullContext}
Project Type: ${projectType}
File Structure: ${JSON.stringify(projectContext.fileStructure, null, 2)}

Analyze this project and determine the most appropriate deployment targets:

Available Platforms:
- Web: Netlify, Vercel, GitHub Pages (for static sites)
- Mobile: PWA (Progressive Web App)
- Desktop: Electron wrapper (if applicable)

Consider:
1. Project type and technology stack
2. Resource requirements
3. Target audience and accessibility
4. Free tier limitations
5. Scalability needs

For each deployment target, specify:
- Platform type (web/mobile/desktop)
- Environment (production/staging)
- Specific configuration requirements
- Build process needs

Prioritize free platforms and ensure the project can be deployed without manual intervention.
`;
  }

  /**
   * Generate deployment configurations
   */
  private async generateDeploymentConfigurations(
    targets: DeploymentTarget[],
    projectContext: ProjectContext
  ): Promise<DeploymentResult[]> {
    const results: DeploymentResult[] = [];

    for (const target of targets) {
      try {
        const result = await this.createDeploymentConfiguration(target, projectContext);
        results.push(result);
        await this.logActivity(`Generated ${target.platform} deployment configuration`, 'info');
      } catch (error) {
        await this.logActivity(`Failed to configure ${target.platform} deployment`, 'error');
        results.push({
          target,
          status: 'failed',
          deploymentLogs: [`Configuration failed: ${error}`],
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    return results;
  }

  /**
   * Create deployment configuration for a specific target
   */
  private async createDeploymentConfiguration(
    target: DeploymentTarget,
    projectContext: ProjectContext
  ): Promise<DeploymentResult> {
    switch (target.platform) {
      case 'web':
        return this.configureWebDeployment(target, projectContext);
      case 'mobile-android':
      case 'mobile-ios':
        return this.configureMobileDeployment(target, projectContext);
      case 'desktop-windows':
      case 'desktop-mac':
      case 'desktop-linux':
        return this.configureDesktopDeployment(target, projectContext);
      default:
        throw new Error(`Unsupported platform: ${target.platform}`);
    }
  }

  /**
   * Configure web deployment
   */
  private configureWebDeployment(target: DeploymentTarget, projectContext: ProjectContext): DeploymentResult {
    const projectName = projectContext.name.toLowerCase().replace(/\s+/g, '-');
    
    return {
      target,
      status: 'success',
      url: `https://${projectName}.netlify.app`,
      buildArtifacts: [
        'netlify.toml',
        '_redirects',
        'package.json',
        'dist/'
      ],
      deploymentLogs: [
        'Generated Netlify configuration',
        'Created build settings',
        'Configured redirects for SPA',
        'Ready for deployment'
      ]
    };
  }

  /**
   * Configure mobile deployment (PWA)
   */
  private configureMobileDeployment(target: DeploymentTarget, projectContext: ProjectContext): DeploymentResult {
    return {
      target,
      status: 'success',
      buildArtifacts: [
        'manifest.json',
        'sw.js',
        'icons/',
        'dist/'
      ],
      deploymentLogs: [
        'Generated PWA manifest',
        'Created service worker',
        'Generated app icons',
        'PWA ready for installation'
      ]
    };
  }

  /**
   * Configure desktop deployment
   */
  private configureDesktopDeployment(target: DeploymentTarget, projectContext: ProjectContext): DeploymentResult {
    return {
      target,
      status: 'success',
      buildArtifacts: [
        'electron-builder.json',
        'main.js',
        'preload.js',
        'dist/'
      ],
      deploymentLogs: [
        'Generated Electron configuration',
        'Created main process file',
        'Configured build settings',
        'Ready for desktop packaging'
      ]
    };
  }

  /**
   * Generate build commands
   */
  private generateBuildCommands(projectContext: ProjectContext): string[] {
    const projectType = this.inferProjectType(projectContext);
    const commands: string[] = [];

    if (projectType.includes('React') || projectType.includes('Vite')) {
      commands.push('npm install');
      commands.push('npm run build');
    } else if (projectType.includes('Node.js')) {
      commands.push('npm install');
      commands.push('npm run build');
    } else {
      commands.push('npm install');
      commands.push('npm run build');
    }

    return commands;
  }

  /**
   * Generate deployment instructions
   */
  private generateDeploymentInstructions(results: DeploymentResult[], projectContext: ProjectContext): string[] {
    const instructions: string[] = [];

    instructions.push('# Deployment Instructions');
    instructions.push('');

    for (const result of results) {
      if (result.status === 'success') {
        instructions.push(`## ${result.target.platform.toUpperCase()} Deployment`);
        instructions.push('');
        
        switch (result.target.platform) {
          case 'web':
            instructions.push('1. Connect your repository to Netlify');
            instructions.push('2. Set build command: npm run build');
            instructions.push('3. Set publish directory: dist');
            instructions.push('4. Deploy automatically on git push');
            break;
          case 'mobile-android':
          case 'mobile-ios':
            instructions.push('1. Deploy web version first');
            instructions.push('2. Users can install as PWA from browser');
            instructions.push('3. Add to home screen for app-like experience');
            break;
          case 'desktop-windows':
          case 'desktop-mac':
          case 'desktop-linux':
            instructions.push('1. Install Electron dependencies');
            instructions.push('2. Run: npm run electron:build');
            instructions.push('3. Distribute generated installer');
            break;
        }
        instructions.push('');
      }
    }

    return instructions;
  }

  /**
   * Generate access URLs
   */
  private generateAccessUrls(results: DeploymentResult[]): string[] {
    return results
      .filter(result => result.status === 'success' && result.url)
      .map(result => result.url!);
  }

  /**
   * Utility methods
   */
  private inferProjectType(projectContext: ProjectContext): string {
    const context = projectContext.fullContext.toLowerCase();
    const fileStructure = JSON.stringify(projectContext.fileStructure).toLowerCase();
    
    if (fileStructure.includes('vite.config') || context.includes('vite')) return 'React Vite';
    if (fileStructure.includes('package.json') && context.includes('react')) return 'React';
    if (fileStructure.includes('server.js') || context.includes('express')) return 'Node.js Express';
    if (context.includes('game')) return 'Web Game';
    return 'Web Application';
  }

  /**
   * Validation methods
   */
  private validateDeploymentTargets(data: any): data is {targets: DeploymentTarget[]} {
    return (
      typeof data === 'object' &&
      data !== null &&
      'targets' in data &&
      Array.isArray(data.targets) &&
      data.targets.every((target: any) =>
        typeof target === 'object' &&
        'platform' in target &&
        'environment' in target &&
        'configuration' in target
      )
    );
  }

  /**
   * System instructions
   */
  private getDeploymentAnalysisSystemInstruction(): string {
    return `You are a Deployment Manager Agent responsible for analyzing projects and determining optimal deployment strategies.

Analyze the project and recommend deployment targets based on:

1. **Project Type**: Web app, mobile app, desktop app, game, etc.
2. **Technology Stack**: React, Node.js, static HTML, etc.
3. **Resource Requirements**: Compute, storage, bandwidth needs
4. **Target Audience**: Web users, mobile users, desktop users
5. **Free Platform Availability**: Netlify, Vercel, GitHub Pages, PWA

For each deployment target, specify:
- platform: 'web' | 'mobile-android' | 'mobile-ios' | 'desktop-windows' | 'desktop-mac' | 'desktop-linux'
- environment: 'development' | 'staging' | 'production'
- configuration: Platform-specific settings and requirements

Prioritize free platforms and ensure deployments can be automated without manual intervention.

Respond with JSON: {"targets": [DeploymentTarget array]}`;
  }
}
