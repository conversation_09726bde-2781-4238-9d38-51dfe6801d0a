{"version": 3, "file": "DependencyResolverAgent.js", "sourceRoot": "", "sources": ["../../../agents/DependencyResolverAgent.ts"], "names": [], "mappings": "AAAA,OAAO,EAIL,SAAS,EACV,MAAM,gBAAgB,CAAC;AAYxB;;;GAGG;AACH,MAAM,OAAO,uBAAuB;IAClC,YACU,aAAmC,EACnC,gBAAyC;QADzC,kBAAa,GAAb,aAAa,CAAsB;QACnC,qBAAgB,GAAhB,gBAAgB,CAAyB;IAChD,CAAC;IAEJ;;OAEG;IACK,KAAK,CAAC,WAAW,CAAC,OAAe,EAAE,MAAgD;QACzF,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,2BAA2B,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAC1F,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,QAAgB,EAAE,SAAiB,EAAE,UAAmB,EAAE,MAAe;QAC3F,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,SAAS,CAAC,mBAAmB,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;IACpH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,mBAAmB,CAC9B,cAA8B,EAC9B,SAAiB,EACjB,MAAe;QAEf,MAAM,IAAI,CAAC,WAAW,CAAC,mCAAmC,EAAE,SAAS,CAAC,CAAC;QAEvE,IAAI,CAAC;YACH,yDAAyD;YACzD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;YAE/E,+BAA+B;YAC/B,MAAM,kBAAkB,GAAG,IAAI,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAC;YAEzE,+BAA+B;YAC/B,MAAM,kBAAkB,GAAG,IAAI,CAAC,0BAA0B,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;YAEzF,iCAAiC;YACjC,MAAM,oBAAoB,GAAG,IAAI,CAAC,4BAA4B,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;YAE7F,MAAM,QAAQ,GAAyC;gBACrD,YAAY;gBACZ,kBAAkB;gBAClB,kBAAkB;gBAClB,oBAAoB;aACrB,CAAC;YAEF,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,YAAY,CAAC,MAAM,4BAA4B,EAAE,SAAS,CAAC,CAAC;YAC/F,IAAI,CAAC,WAAW,CACd,uBAAuB,EACvB,6BAA6B,YAAY,CAAC,MAAM,6DAA6D,EAC7G,SAAS,EACT,MAAM,CACP,CAAC;YAEF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,CAAC,WAAW,CAAC,iCAAiC,YAAY,EAAE,EAAE,OAAO,CAAC,CAAC;YACjF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAC/B,cAA8B,EAC9B,SAAiB;QAEjB,MAAM,MAAM,GAAG,IAAI,CAAC,6BAA6B,CAAC,cAAc,CAAC,CAAC;QAElE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAC5D,SAAS,EACT,MAAM,EACN,IAAI,CAAC,sCAAsC,EAAE,EAC7C,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAC9C,GAAG,CACJ,CAAC;QAEF,OAAO,QAAQ,CAAC,YAAY,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,6BAA6B,CAAC,cAA8B;QAClE,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;QAE1D,OAAO;mBACQ,cAAc,CAAC,WAAW;gBAC7B,WAAW;kBACT,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;8BACzC,cAAc,CAAC,wBAAwB,IAAI,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BvF,CAAC;IACA,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,YAA8B;QAC/D,MAAM,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC;QAC/D,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9F,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;QAE9F,IAAI,MAAM,GAAG,iBAAiB,CAAC;QAC/B,MAAM,IAAI,oCAAoC,CAAC;QAC/C,MAAM,IAAI,yDAAyD,CAAC;QAEpE,MAAM,IAAI,+CAA+C,CAAC;QAE1D,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,6BAA6B,CAAC;YACxC,MAAM,IAAI,eAAe,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;QACpD,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,8BAA8B,CAAC;YACzC,MAAM,IAAI,0BAA0B,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;QAC9D,CAAC;QAED,0BAA0B;QAC1B,MAAM,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC;QAC/D,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,qCAAqC,CAAC;YAChD,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACpB,MAAM,IAAI,+CAA+C,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,OAAO,eAAe,CAAC;YAClG,CAAC,CAAC,CAAC;YACH,MAAM,IAAI,IAAI,CAAC;QACjB,CAAC;QAED,MAAM,IAAI,+CAA+C,CAAC;QAC1D,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,YAA8B,EAAE,cAA8B;QAC/F,MAAM,WAAW,GAA2C,EAAE,CAAC;QAE/D,gCAAgC;QAChC,MAAM,kBAAkB,GAAG,IAAI,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAC;QACzE,IAAI,kBAAkB,EAAE,CAAC;YACvB,WAAW,CAAC,IAAI,CAAC;gBACf,IAAI,EAAE,cAAc;gBACpB,OAAO,EAAE,kBAAkB;aAC5B,CAAC,CAAC;QACL,CAAC;QAED,uCAAuC;QACvC,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,YAAY,CAAC,EAAE,CAAC;YACxD,WAAW,CAAC,IAAI,CAAC;gBACf,IAAI,EAAE,eAAe;gBACrB,OAAO,EAAE,IAAI,CAAC,wBAAwB,CAAC,YAAY,EAAE,cAAc,CAAC;aACrE,CAAC,CAAC;QACL,CAAC;QAED,iCAAiC;QACjC,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,EAAE,CAAC;YAClD,WAAW,CAAC,IAAI,CAAC;gBACf,IAAI,EAAE,gBAAgB;gBACtB,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC;aAC/D,CAAC,CAAC;QACL,CAAC;QAED,mCAAmC;QACnC,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;YAC1D,WAAW,CAAC,IAAI,CAAC;gBACf,IAAI,EAAE,gBAAgB;gBACtB,OAAO,EAAE,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC;aACjD,CAAC,CAAC;QACL,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,YAA8B;QAC/D,MAAM,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC;QAC/D,MAAM,QAAQ,GAA2B,EAAE,CAAC;QAC5C,MAAM,OAAO,GAA2B,EAAE,CAAC;QAE3C,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACpB,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC;gBACjB,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC;YAClC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG;YAClB,YAAY,EAAE,QAAQ;YACtB,eAAe,EAAE,OAAO;YACxB,OAAO,EAAE;gBACP,GAAG,EAAE,MAAM;gBACX,KAAK,EAAE,mBAAmB;gBAC1B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,2EAA2E;aAClF;SACF,CAAC;QAEF,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,YAA8B,EAAE,cAA8B;QAC7F,MAAM,MAAM,GAAG;YACb,eAAe,EAAE;gBACf,MAAM,EAAE,QAAQ;gBAChB,uBAAuB,EAAE,IAAI;gBAC7B,GAAG,EAAE,CAAC,KAAK,EAAE,cAAc,EAAE,QAAQ,CAAC;gBACtC,OAAO,EAAE,KAAK;gBACd,YAAY,EAAE,IAAI;gBAClB,eAAe,EAAE,KAAK;gBACtB,4BAA4B,EAAE,IAAI;gBAClC,MAAM,EAAE,IAAI;gBACZ,gCAAgC,EAAE,IAAI;gBACtC,MAAM,EAAE,QAAQ;gBAChB,gBAAgB,EAAE,MAAM;gBACxB,iBAAiB,EAAE,IAAI;gBACvB,eAAe,EAAE,IAAI;gBACrB,MAAM,EAAE,IAAI;gBACZ,GAAG,EAAE,WAAW;aACjB;YACD,OAAO,EAAE,CAAC,KAAK,CAAC;YAChB,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,sBAAsB,EAAE,CAAC;SAC/C,CAAC;QAEF,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,YAA8B,EAAE,cAA8B;QACvF,OAAO;;;;;;;;;;;;;GAaR,CAAC;IACF,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,YAA8B;QACzD,MAAM,MAAM,GAAG;YACb,IAAI,EAAE,IAAI;YACV,GAAG,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;YACpC,OAAO,EAAE;gBACP,oBAAoB;gBACpB,gCAAgC;gBAChC,0BAA0B;gBAC1B,gCAAgC;aACjC;YACD,cAAc,EAAE,CAAC,MAAM,EAAE,eAAe,CAAC;YACzC,MAAM,EAAE,2BAA2B;YACnC,OAAO,EAAE,CAAC,eAAe,CAAC;YAC1B,KAAK,EAAE;gBACL,sCAAsC,EAAE;oBACtC,MAAM;oBACN,EAAE,mBAAmB,EAAE,IAAI,EAAE;iBAC9B;gBACD,0BAA0B,EAAE,KAAK;aAClC;SACF,CAAC;QAEF,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,4BAA4B,CAAC,YAA8B,EAAE,cAA8B;QACjG,MAAM,OAAO,GAA2B,EAAE,CAAC;QAE3C,mCAAmC;QACnC,OAAO,CAAC,QAAQ,GAAG,aAAa,CAAC;QACjC,OAAO,CAAC,aAAa,GAAG,cAAc,CAAC,IAAI,CAAC;QAC5C,OAAO,CAAC,gBAAgB,GAAG,OAAO,CAAC;QAEnC,qCAAqC;QACrC,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;YAC1F,OAAO,CAAC,sBAAsB,GAAG,6BAA6B,CAAC;YAC/D,OAAO,CAAC,wBAAwB,GAAG,+BAA+B,CAAC;QACrE,CAAC;QAED,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;YAC7F,OAAO,CAAC,qBAAqB,GAAG,4BAA4B,CAAC;YAC7D,OAAO,CAAC,oBAAoB,GAAG,2BAA2B,CAAC;QAC7D,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,cAA8B;QACrD,MAAM,OAAO,GAAG,cAAc,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;QACzD,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;QAE/C,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,kBAAkB,CAAC;QACjF,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,WAAW,CAAC;QAC7E,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,qBAAqB,CAAC;QAC1F,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,wBAAwB,CAAC;QAC3F,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,IAAS;QACpC,OAAO,CACL,OAAO,IAAI,KAAK,QAAQ;YACxB,IAAI,KAAK,IAAI;YACb,cAAc,IAAI,IAAI;YACtB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC;YAChC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,GAAQ,EAAE,EAAE,CACnC,OAAO,GAAG,KAAK,QAAQ;gBACvB,MAAM,IAAI,GAAG;gBACb,SAAS,IAAI,GAAG;gBAChB,MAAM,IAAI,GAAG;gBACb,SAAS,IAAI,GAAG;gBAChB,UAAU,IAAI,GAAG,CAClB,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,sCAAsC;QAC5C,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;4DA4BiD,CAAC;IAC3D,CAAC;CACF"}