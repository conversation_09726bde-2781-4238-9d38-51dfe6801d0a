{"version": 3, "file": "BackendCoderAgent.js", "sourceRoot": "", "sources": ["../../../agents/BackendCoderAgent.ts"], "names": [], "mappings": "AACA,OAAO,EAIL,WAAW,EAEZ,MAAM,gBAAgB,CAAC;AAWxB;;;GAGG;AACH,MAAM,OAAO,iBAAiB;IAI5B,YAAY,aAAmC,EAAE,gBAAyC;QACxF,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;IAC3C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW,CAAC,OAAe,EAAE,MAAgD;QACzF,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,aAAa,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,QAAgB,EAAE,SAAiB,EAAE,UAAmB,EAAE,MAAe;QAC3F,kDAAkD;QAClD,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;IACpG,CAAC;IAED;;;;;;;;;;;OAWG;IACI,KAAK,CAAC,mBAAmB,CAC9B,WAAmB,EACnB,QAAgB,EAChB,eAAuB,EACvB,oBAA4B,EAC5B,aAAyB,EACzB,SAAiB,EACjB,WAAyB,EACzB,iBAA0B;QAE1B,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,WAAW,CAAC,sCAAsC,QAAQ,EAAE,EAAE,SAAS,CAAC,CAAC;YACpF,IAAI,CAAC,WAAW,CAAC,yBAAyB,EAAE,SAAS,QAAQ,kBAAkB,eAAe,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,eAAe,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,gBAAgB,SAAS,EAAE,CAAC,CAAC;YAE7L,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,+BAA+B,CAAC,aAAa,CAAC,CAAC;YAC9F,IAAI,aAAa,GAAG,EAAE,CAAC;YACvB,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,WAAW,CAAC,WAAW,EAAE,CAAC;gBAC9D,aAAa,GAAG,qBAAqB,WAAW,CAAC,IAAI,wEAAwE,CAAC;YAClI,CAAC;YAED,IAAI,oBAAoB,GAAG,EAAE,CAAC;YAC9B,IAAI,iBAAiB,EAAE,CAAC;gBACpB,oBAAoB,GAAG,uEAAuE,iBAAiB,EAAE,CAAC;YACtH,CAAC;YAED,MAAM,cAAc,GAAG;wBACL,WAAW;iCACF,QAAQ;oCACL,eAAe;2BACxB,oBAAoB;UACrC,mBAAmB,GAAG,aAAa,GAAG,oBAAoB;;OAE7D,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAC5D,SAAS,EACT,cAAc,EACd,qKAAqK,EACrK,CAAC,IAAS,EAAkC,EAAE;gBAC5C,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI;oBACzC,MAAM,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ;oBAC/C,CAAC,IAAI,CAAC,qBAAqB,KAAK,IAAI,IAAI,OAAO,IAAI,CAAC,qBAAqB,KAAK,WAAW,IAAI,OAAO,IAAI,CAAC,qBAAqB,KAAK,QAAQ,CAAC,CAAC;YACtJ,CAAC,EACD,GAAG,EACH,IAAI,CACL,CAAC;YAEF,MAAM,IAAI,CAAC,WAAW,CAAC,mCAAmC,QAAQ,KAAK,QAAQ,CAAC,IAAI,CAAC,MAAM,cAAc,EAAE,SAAS,CAAC,CAAC;YACtH,IAAI,CAAC,WAAW,CAAC,2BAA2B,EAAE,aAAa,QAAQ,CAAC,IAAI,CAAC,MAAM,2BAA2B,QAAQ,EAAE,EAAE,wCAAwC,CAAC,CAAC;YAEhK,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,CAAC,WAAW,CAAC,+BAA+B,QAAQ,KAAK,YAAY,EAAE,EAAE,OAAO,CAAC,CAAC;YAC5F,IAAI,CAAC,WAAW,CAAC,wBAAwB,EAAE,oBAAoB,QAAQ,KAAK,YAAY,EAAE,EAAE,sCAAsC,CAAC,CAAC;YACpI,OAAO,CAAC,KAAK,CAAC,yDAAyD,QAAQ,KAAK,EAAE,KAAK,CAAC,CAAC;YAC7F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;;;;OASG;IACI,KAAK,CAAC,gBAAgB,CAC3B,cAAsB,EACtB,YAAoB,EACpB,mBAA2B,EAC3B,kBAA4B,EAC5B,aAAyB,EACzB,SAAiB;QAEjB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,WAAW,CAAC,sCAAsC,YAAY,EAAE,EAAE,SAAS,CAAC,CAAC;YACxF,IAAI,CAAC,WAAW,CAAC,8BAA8B,EAAE,cAAc,YAAY,oBAAoB,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,gBAAgB,SAAS,EAAE,CAAC,CAAC;YAE7J,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,+BAA+B,CAAC,aAAa,CAAC,CAAC;YAC9F,MAAM,cAAc,GAAG;2BACF,cAAc;UAC/B,mBAAmB;sCACS,YAAY;yCACT,mBAAmB;gCAC5B,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC;;OAEtD,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAC5D,SAAS,EACT,cAAc,EACd,wKAAwK,EACxK,CAAC,IAAS,EAAkC,EAAE;gBAC5C,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI;oBACzC,MAAM,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ;oBAC/C,CAAC,IAAI,CAAC,qBAAqB,KAAK,IAAI,IAAI,OAAO,IAAI,CAAC,qBAAqB,KAAK,WAAW,IAAI,OAAO,IAAI,CAAC,qBAAqB,KAAK,QAAQ,CAAC,CAAC;YACtJ,CAAC,EACD,GAAG,EACH,IAAI,CACL,CAAC;YAEF,MAAM,IAAI,CAAC,WAAW,CAAC,wCAAwC,YAAY,KAAK,QAAQ,CAAC,IAAI,CAAC,MAAM,cAAc,EAAE,SAAS,CAAC,CAAC;YAC/H,IAAI,CAAC,WAAW,CAAC,gCAAgC,EAAE,aAAa,QAAQ,CAAC,IAAI,CAAC,MAAM,gCAAgC,YAAY,EAAE,EAAE,6CAA6C,CAAC,CAAC;YAEnL,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,CAAC,WAAW,CAAC,oCAAoC,YAAY,KAAK,YAAY,EAAE,EAAE,OAAO,CAAC,CAAC;YACrG,IAAI,CAAC,WAAW,CAAC,6BAA6B,EAAE,kCAAkC,YAAY,KAAK,YAAY,EAAE,EAAE,2CAA2C,CAAC,CAAC;YAChK,OAAO,CAAC,KAAK,CAAC,sDAAsD,YAAY,KAAK,EAAE,KAAK,CAAC,CAAC;YAC9F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,aAAa,CACxB,WAAmB,EACnB,SAAiB;QAEjB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,WAAW,CAAC,8BAA8B,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YACnG,IAAI,CAAC,WAAW,CAAC,0BAA0B,EAAE,WAAW,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,EAAE,gBAAgB,SAAS,EAAE,CAAC,CAAC;YAEzH,mFAAmF;YACnF,yEAAyE;YACzE,MAAM,kBAAkB,GAAG,wXAAwX,CAAC;YAEpZ,MAAM,IAAI,CAAC,WAAW,CAAC,0CAA0C,EAAE,SAAS,CAAC,CAAC;YAC9E,IAAI,CAAC,WAAW,CAAC,4BAA4B,EAAE,6BAA6B,EAAE,6CAA6C,CAAC,CAAC;YAE7H,OAAO;gBACL,OAAO,EAAE,kBAAkB;gBAC3B,WAAW,EAAE,wBAAwB,WAAW,EAAE;aACnD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,CAAC,WAAW,CAAC,6BAA6B,YAAY,EAAE,EAAE,OAAO,CAAC,CAAC;YAC7E,IAAI,CAAC,WAAW,CAAC,yBAAyB,EAAE,UAAU,YAAY,EAAE,EAAE,uCAAuC,CAAC,CAAC;YAC/G,OAAO,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;YACpE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF"}