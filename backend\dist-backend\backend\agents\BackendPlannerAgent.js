/**
 * Backend version of PlannerAgent that runs on the server
 * Handles project planning and task generation
 */
export class BackendPlannerAgent {
    constructor(geminiService, loggingInterface) {
        this.geminiService = geminiService;
        this.loggingInterface = loggingInterface;
    }
    /**
     * Log activity to the frontend
     */
    async logActivity(message, status) {
        await this.loggingInterface.addCompanyLog('Planner Agent', message, status);
    }
    /**
     * Log decision to the frontend
     */
    logDecision(decision, reasoning, stackTrace, taskId) {
        // Fire and forget - don't await to avoid blocking
        this.loggingInterface.addDecisionLogEntry('Planner Agent', decision, reasoning, stackTrace, taskId);
    }
    /**
     * Generates the initial project plan, including tasks and file structure.
     * @param projectIdea - The user's idea for the project.
     * @param modelName - The name of the Gemini model to use.
     * @param projectContext - The current full context of the project.
     * @param licenseInfo - The chosen license information for the project.
     * @returns A promise that resolves to the planner's response.
     */
    async generatePlan(projectIdea, modelName, projectContext, licenseInfo) {
        try {
            await this.logActivity(`Generating initial plan for project: ${projectIdea}`, 'working');
            const plan = await this.geminiService.generateInitialPlan(projectIdea, modelName, projectContext, licenseInfo);
            await this.logActivity(`Initial plan generated successfully. Tasks: ${plan.tasks.length}, Files: ${plan.fileStructure?.length || 0}`, 'success');
            this.logDecision('Plan Generated', `Created ${plan.tasks.length} tasks and ${plan.fileStructure?.length || 0} file structure items`, 'Plan generation completed successfully');
            return plan;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            await this.logActivity(`Failed to generate initial plan: ${errorMessage}`, 'error');
            this.logDecision('Plan Generation Failed', `Error: ${errorMessage}`, 'Plan generation encountered an error');
            console.error("BackendPlannerAgent: Error generating plan -", error);
            throw error;
        }
    }
    /**
     * Generates tasks based on user feedback.
     * @param projectContext - The current full context of the project.
     * @param projectIdea - The original project idea.
     * @param fileStructure - The current file structure.
     * @param feedback - The user's feedback.
     * @param modelName - The name of the Gemini model to use.
     * @returns A promise that resolves to a planner response containing new tasks.
     */
    async generateTasksFromFeedback(projectContext, projectIdea, fileStructure, feedback, modelName) {
        try {
            await this.logActivity(`Generating tasks from user feedback: ${feedback.description.substring(0, 50)}...`, 'working');
            const response = await this.geminiService.generateTasksFromUserFeedback(projectContext, projectIdea, fileStructure, feedback, modelName);
            await this.logActivity(`Generated ${response.tasks.length} tasks from user feedback`, 'success');
            this.logDecision('Tasks Generated from Feedback', `Created ${response.tasks.length} tasks based on user feedback: ${feedback.description.substring(0, 100)}...`);
            return response;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            await this.logActivity(`Failed to generate tasks from feedback: ${errorMessage}`, 'error');
            this.logDecision('Feedback Task Generation Failed', `Error: ${errorMessage}`, 'Task generation from feedback encountered an error');
            console.error("BackendPlannerAgent: Error generating tasks from feedback -", error);
            throw error;
        }
    }
    /**
     * Reviews and refines an initial project plan.
     * @param projectIdea - The original project idea.
     * @param initialTasks - The initial tasks to review.
     * @param initialFileStructure - The initial file structure to review.
     * @param projectContext - The current project context.
     * @param modelName - The name of the Gemini model to use.
     * @param technologyStackSuggestion - Optional technology stack suggestion.
     * @returns A promise that resolves to the refined plan response.
     */
    async reviewAndRefinePlan(projectIdea, initialTasks, initialFileStructure, projectContext, modelName, technologyStackSuggestion) {
        try {
            await this.logActivity('Reviewing and refining initial project plan...', 'working');
            const response = await this.geminiService.reviewAndRefinePlan(projectIdea, initialTasks, initialFileStructure, projectContext, modelName, technologyStackSuggestion);
            await this.logActivity('Plan review and refinement completed successfully', 'success');
            this.logDecision('Plan Reviewed and Refined', `Refined plan: ${response.tasks.length} tasks, ${response.fileStructure?.length || 0} files. Review notes: ${response.reviewNotes || 'None'}`);
            return response;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            await this.logActivity(`Failed to review and refine plan: ${errorMessage}`, 'error');
            this.logDecision('Plan Review Failed', `Error: ${errorMessage}`, 'Plan review and refinement encountered an error');
            console.error("BackendPlannerAgent: Error reviewing plan -", error);
            throw error;
        }
    }
    /**
     * Validates a project build configuration.
     * @param projectContext - The current project context.
     * @param fileStructure - The current file structure.
     * @param packageJsonContent - Content of package.json if available.
     * @param modelName - The name of the Gemini model to use.
     * @returns A promise that resolves to the build validation response.
     */
    async validateProjectBuild(projectContext, fileStructure, packageJsonContent, modelName) {
        try {
            await this.logActivity('Validating project build configuration...', 'working');
            const response = await this.geminiService.validateProjectBuild(projectContext, fileStructure, packageJsonContent, modelName);
            const hasIssues = response.buildIssues && response.buildIssues.length > 0;
            const status = hasIssues ? 'error' : 'success';
            const message = hasIssues
                ? `Project build validation failed with ${response.buildIssues.length} issues`
                : 'Project build validation passed';
            await this.logActivity(message, status);
            this.logDecision('Build Validation Completed', `Issues: ${response.buildIssues?.length || 0}, Summary: ${response.validationSummary}`);
            return response;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            await this.logActivity(`Failed to validate project build: ${errorMessage}`, 'error');
            this.logDecision('Build Validation Failed', `Error: ${errorMessage}`, 'Build validation encountered an error');
            console.error("BackendPlannerAgent: Error validating build -", error);
            throw error;
        }
    }
}
//# sourceMappingURL=BackendPlannerAgent.js.map