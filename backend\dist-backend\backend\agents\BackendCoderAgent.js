import { LicenseType } from '../../types.js';
/**
 * Backend version of CoderAgent that runs on the server
 * Handles code generation for project files
 */
export class BackendCoderAgent {
    constructor(geminiService, loggingInterface) {
        this.geminiService = geminiService;
        this.loggingInterface = loggingInterface;
    }
    /**
     * Log activity to the frontend
     */
    async logActivity(message, status) {
        await this.loggingInterface.addCompanyLog('Coder Agent', message, status);
    }
    /**
     * Log decision to the frontend
     */
    logDecision(decision, reasoning, stackTrace, taskId) {
        // Fire and forget - don't await to avoid blocking
        this.loggingInterface.addDecisionLogEntry('Coder Agent', decision, reasoning, stackTrace, taskId);
    }
    /**
     * Generates source code for a specific file based on project requirements.
     * @param projectIdea - The original project idea.
     * @param filePath - The path of the file to generate.
     * @param fileDescription - Description of what the file should contain.
     * @param projectContextString - The current project context.
     * @param fileStructure - The current file structure of the project.
     * @param modelName - The name of the Gemini model to use.
     * @param licenseInfo - Optional license information for the project.
     * @param clarifierResponse - Optional clarification response from the clarifier agent.
     * @returns A promise that resolves to the generated code response.
     */
    async generateFileContent(projectIdea, filePath, fileDescription, projectContextString, fileStructure, modelName, licenseInfo, clarifierResponse) {
        try {
            await this.logActivity(`Starting code generation for file: ${filePath}`, 'working');
            this.logDecision('Code Generation Started', `File: ${filePath}, Description: ${fileDescription.substring(0, 100)}${fileDescription.length > 100 ? '...' : ''}`, `Using model: ${modelName}`);
            const fileStructurePrompt = this.geminiService.serializeFileStructureForPrompt(fileStructure);
            let licenseHeader = '';
            if (licenseInfo && licenseInfo.type !== LicenseType.Unspecified) {
                licenseHeader = `\n\nLicense Type: ${licenseInfo.type}. Include appropriate license header at the top of the file if needed.`;
            }
            let clarificationContext = '';
            if (clarifierResponse) {
                clarificationContext = `\n\nClarification Response (use this to guide your implementation): ${clarifierResponse}`;
            }
            const originalPrompt = `
        Project Idea: ${projectIdea}
        File Path To Generate: ${filePath}
        File Description/Purpose: ${fileDescription}
        Project Context: ${projectContextString}
        ${fileStructurePrompt}${licenseHeader}${clarificationContext}
        Generate the complete code for this file.
      `;
            const response = await this.geminiService.makeRequestWithRetry(modelName, originalPrompt, "You are a Code Generator Agent. Generate complete, functional code for the specified file. Return a JSON object with 'code' property containing the generated code.", (data) => {
                return typeof data === 'object' && data !== null &&
                    'code' in data && typeof data.code === 'string' &&
                    (data.clarificationQuestion === null || typeof data.clarificationQuestion === 'undefined' || typeof data.clarificationQuestion === 'string');
            }, 0.5, true);
            await this.logActivity(`Successfully generated code for ${filePath} (${response.code.length} characters)`, 'success');
            this.logDecision('Code Generation Completed', `Generated ${response.code.length} characters of code for ${filePath}`, 'Code generation completed successfully');
            return response;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            await this.logActivity(`Failed to generate code for ${filePath}: ${errorMessage}`, 'error');
            this.logDecision('Code Generation Failed', `Error generating ${filePath}: ${errorMessage}`, 'Code generation encountered an error');
            console.error(`BackendCoderAgent: Error generating file content for "${filePath}" -`, error);
            throw error;
        }
    }
    /**
     * Generates test code for a specific test file.
     * @param projectContext - The current project context.
     * @param testFilePath - The path of the test file to generate.
     * @param testFileDescription - Description of what the test file should contain.
     * @param relatedSourceFiles - Array of related source file paths.
     * @param fileStructure - The current file structure of the project.
     * @param modelName - The name of the Gemini model to use.
     * @returns A promise that resolves to the generated test code response.
     */
    async generateTestCode(projectContext, testFilePath, testFileDescription, relatedSourceFiles, fileStructure, modelName) {
        try {
            await this.logActivity(`Starting test code generation for: ${testFilePath}`, 'working');
            this.logDecision('Test Code Generation Started', `Test file: ${testFilePath}, Related files: ${relatedSourceFiles.join(', ')}`, `Using model: ${modelName}`);
            const fileStructurePrompt = this.geminiService.serializeFileStructureForPrompt(fileStructure);
            const originalPrompt = `
        Project Context: ${projectContext}
        ${fileStructurePrompt}
        Test File Path To Generate: ${testFilePath}
        Test File Description/Purpose: ${testFileDescription}
        Related Source Files: ${relatedSourceFiles.join(', ')}
        Generate the test code for this file.
      `;
            const response = await this.geminiService.makeRequestWithRetry(modelName, originalPrompt, "You are a Test Code Generator Agent. Generate comprehensive test code for the specified test file. Return a JSON object with 'code' property containing the test code.", (data) => {
                return typeof data === 'object' && data !== null &&
                    'code' in data && typeof data.code === 'string' &&
                    (data.clarificationQuestion === null || typeof data.clarificationQuestion === 'undefined' || typeof data.clarificationQuestion === 'string');
            }, 0.5, true);
            await this.logActivity(`Successfully generated test code for ${testFilePath} (${response.code.length} characters)`, 'success');
            this.logDecision('Test Code Generation Completed', `Generated ${response.code.length} characters of test code for ${testFilePath}`, 'Test code generation completed successfully');
            return response;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            await this.logActivity(`Failed to generate test code for ${testFilePath}: ${errorMessage}`, 'error');
            this.logDecision('Test Code Generation Failed', `Error generating test code for ${testFilePath}: ${errorMessage}`, 'Test code generation encountered an error');
            console.error(`BackendCoderAgent: Error generating test code for "${testFilePath}" -`, error);
            throw error;
        }
    }
    /**
     * Generates image content using AI image generation.
     * @param imagePrompt - The prompt for image generation.
     * @param modelName - The name of the image generation model to use.
     * @returns A promise that resolves to the generated image data.
     */
    async generateImage(imagePrompt, modelName) {
        try {
            await this.logActivity(`Starting image generation: ${imagePrompt.substring(0, 50)}...`, 'working');
            this.logDecision('Image Generation Started', `Prompt: ${imagePrompt.substring(0, 100)}...`, `Using model: ${modelName}`);
            // For now, return a placeholder since image generation requires different handling
            // This would need to be implemented with the actual image generation API
            const placeholderDataUrl = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjY2NjIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIFBsYWNlaG9sZGVyPC90ZXh0Pgo8L3N2Zz4K';
            await this.logActivity('Image generation completed (placeholder)', 'success');
            this.logDecision('Image Generation Completed', 'Generated placeholder image', 'Image generation completed with placeholder');
            return {
                dataUrl: placeholderDataUrl,
                description: `Generated image for: ${imagePrompt}`
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            await this.logActivity(`Failed to generate image: ${errorMessage}`, 'error');
            this.logDecision('Image Generation Failed', `Error: ${errorMessage}`, 'Image generation encountered an error');
            console.error(`BackendCoderAgent: Error generating image -`, error);
            throw error;
        }
    }
}
//# sourceMappingURL=BackendCoderAgent.js.map