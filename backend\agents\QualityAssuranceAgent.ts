import {
  GeminiJsonQualityAssuranceResponse,
  QualityCheck,
  ProjectContext,
  AgentType
} from '../../types.js';
import { BackendGeminiService } from '../services/BackendGeminiService.js';

/**
 * Interface for backend logging that sends updates to frontend
 */
interface BackendLoggingInterface {
  addCompanyLog: (agent: string, message: string, status: 'info' | 'success' | 'error' | 'working', taskId?: string) => Promise<void>;
  addTaskLog: (taskId: string, agent: string, message: string, status: 'info' | 'success' | 'error' | 'working', stage?: any, subDetailSections?: Array<{title: string, content: string, isCodeBlock?: boolean}>) => Promise<void>;
  addDecisionLogEntry: (agent: string, decision: string, reasoning: string, stackTrace?: string, taskId?: string) => Promise<void>;
}

/**
 * Quality Assurance Agent - Ensures projects are complete and deployment-ready
 * Performs comprehensive quality checks and validation
 */
export class QualityAssuranceAgent {
  constructor(
    private geminiService: BackendGeminiService,
    private loggingInterface: BackendLoggingInterface
  ) {}

  /**
   * Log activity to the frontend
   */
  private async logActivity(message: string, status: 'info' | 'success' | 'error' | 'working'): Promise<void> {
    await this.loggingInterface.addCompanyLog('Quality Assurance Agent', message, status);
  }

  /**
   * Log decision to the frontend
   */
  private logDecision(decision: string, reasoning: string, stackTrace?: string, taskId?: string): void {
    this.loggingInterface.addDecisionLogEntry(AgentType.QUALITY_ASSURANCE, decision, reasoning, stackTrace, taskId);
  }

  /**
   * Perform comprehensive quality assurance on the project
   */
  public async performQualityAssurance(
    projectContext: ProjectContext,
    modelName: string,
    taskId?: string
  ): Promise<GeminiJsonQualityAssuranceResponse> {
    await this.logActivity('Performing comprehensive quality assurance...', 'working');
    
    try {
      // Perform various quality checks
      const functionalityChecks = await this.checkFunctionality(projectContext, modelName);
      const performanceChecks = await this.checkPerformance(projectContext, modelName);
      const securityChecks = await this.checkSecurity(projectContext, modelName);
      const usabilityChecks = await this.checkUsability(projectContext, modelName);
      const compatibilityChecks = await this.checkCompatibility(projectContext, modelName);

      // Combine all checks
      const allChecks = [
        ...functionalityChecks,
        ...performanceChecks,
        ...securityChecks,
        ...usabilityChecks,
        ...compatibilityChecks
      ];

      // Calculate overall score
      const overallScore = this.calculateOverallScore(allChecks);
      
      // Identify critical issues
      const criticalIssues = this.identifyCriticalIssues(allChecks);
      
      // Generate recommendations
      const recommendations = this.generateRecommendations(allChecks, projectContext);
      
      // Determine readiness status
      const readinessStatus = this.determineReadinessStatus(overallScore, criticalIssues);

      const response: GeminiJsonQualityAssuranceResponse = {
        overallScore,
        checks: allChecks,
        criticalIssues,
        recommendations,
        readinessStatus
      };

      await this.logActivity(`Quality assurance completed. Score: ${overallScore}/100`, 'success');
      this.logDecision(
        'Quality Assurance Completed',
        `Project scored ${overallScore}/100 with ${criticalIssues.length} critical issues. Status: ${readinessStatus}`,
        undefined,
        taskId
      );

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Error during quality assurance: ${errorMessage}`, 'error');
      throw error;
    }
  }

  /**
   * Check functionality completeness
   */
  private async checkFunctionality(projectContext: ProjectContext, modelName: string): Promise<QualityCheck[]> {
    const checks: QualityCheck[] = [];

    // Check if all planned features are implemented
    const completedTasks = projectContext.tasks.filter(task => task.status === 'completed');
    const totalTasks = projectContext.tasks.length;
    const completionRate = totalTasks > 0 ? (completedTasks.length / totalTasks) * 100 : 0;

    checks.push({
      category: 'functionality',
      test: 'Feature Completion Rate',
      status: completionRate >= 90 ? 'pass' : completionRate >= 70 ? 'warning' : 'fail',
      details: `${completedTasks.length}/${totalTasks} tasks completed (${completionRate.toFixed(1)}%)`,
      recommendation: completionRate < 90 ? 'Complete remaining tasks before deployment' : undefined
    });

    // Check for placeholder content
    const hasPlaceholders = this.checkForPlaceholders(projectContext);
    checks.push({
      category: 'functionality',
      test: 'Placeholder Content Check',
      status: hasPlaceholders ? 'fail' : 'pass',
      details: hasPlaceholders ? 'Placeholder content found in project files' : 'No placeholder content detected',
      recommendation: hasPlaceholders ? 'Replace all placeholder content with actual implementation' : undefined
    });

    // Check for broken imports/exports
    const hasBrokenImports = this.checkForBrokenImports(projectContext);
    checks.push({
      category: 'functionality',
      test: 'Import/Export Integrity',
      status: hasBrokenImports ? 'fail' : 'pass',
      details: hasBrokenImports ? 'Broken imports or exports detected' : 'All imports and exports appear valid',
      recommendation: hasBrokenImports ? 'Fix broken import/export statements' : undefined
    });

    return checks;
  }

  /**
   * Check performance aspects
   */
  private async checkPerformance(projectContext: ProjectContext, modelName: string): Promise<QualityCheck[]> {
    const checks: QualityCheck[] = [];

    // Check bundle size considerations
    const hasLargeAssets = this.checkForLargeAssets(projectContext);
    checks.push({
      category: 'performance',
      test: 'Asset Size Optimization',
      status: hasLargeAssets ? 'warning' : 'pass',
      details: hasLargeAssets ? 'Large assets detected that may impact loading time' : 'Asset sizes appear optimized',
      recommendation: hasLargeAssets ? 'Consider optimizing large assets or implementing lazy loading' : undefined
    });

    // Check for performance best practices
    const hasPerformanceIssues = this.checkPerformancePractices(projectContext);
    checks.push({
      category: 'performance',
      test: 'Performance Best Practices',
      status: hasPerformanceIssues ? 'warning' : 'pass',
      details: hasPerformanceIssues ? 'Potential performance issues identified' : 'Performance practices look good',
      recommendation: hasPerformanceIssues ? 'Review and optimize performance-critical code paths' : undefined
    });

    return checks;
  }

  /**
   * Check security aspects
   */
  private async checkSecurity(projectContext: ProjectContext, modelName: string): Promise<QualityCheck[]> {
    const checks: QualityCheck[] = [];

    // Check for hardcoded secrets
    const hasHardcodedSecrets = this.checkForHardcodedSecrets(projectContext);
    checks.push({
      category: 'security',
      test: 'Hardcoded Secrets Check',
      status: hasHardcodedSecrets ? 'fail' : 'pass',
      details: hasHardcodedSecrets ? 'Hardcoded secrets or API keys found' : 'No hardcoded secrets detected',
      recommendation: hasHardcodedSecrets ? 'Move all secrets to environment variables' : undefined
    });

    // Check for security headers and practices
    const hasSecurityIssues = this.checkSecurityPractices(projectContext);
    checks.push({
      category: 'security',
      test: 'Security Best Practices',
      status: hasSecurityIssues ? 'warning' : 'pass',
      details: hasSecurityIssues ? 'Security improvements recommended' : 'Security practices look good',
      recommendation: hasSecurityIssues ? 'Implement recommended security headers and practices' : undefined
    });

    return checks;
  }

  /**
   * Check usability aspects
   */
  private async checkUsability(projectContext: ProjectContext, modelName: string): Promise<QualityCheck[]> {
    const checks: QualityCheck[] = [];

    // Check for accessibility features
    const hasAccessibilityIssues = this.checkAccessibility(projectContext);
    checks.push({
      category: 'usability',
      test: 'Accessibility Features',
      status: hasAccessibilityIssues ? 'warning' : 'pass',
      details: hasAccessibilityIssues ? 'Accessibility improvements needed' : 'Basic accessibility features present',
      recommendation: hasAccessibilityIssues ? 'Add ARIA labels, alt text, and keyboard navigation support' : undefined
    });

    // Check for responsive design
    const hasResponsiveDesign = this.checkResponsiveDesign(projectContext);
    checks.push({
      category: 'usability',
      test: 'Responsive Design',
      status: hasResponsiveDesign ? 'pass' : 'warning',
      details: hasResponsiveDesign ? 'Responsive design implemented' : 'Limited responsive design detected',
      recommendation: !hasResponsiveDesign ? 'Implement responsive design for mobile compatibility' : undefined
    });

    return checks;
  }

  /**
   * Check compatibility aspects
   */
  private async checkCompatibility(projectContext: ProjectContext, modelName: string): Promise<QualityCheck[]> {
    const checks: QualityCheck[] = [];

    // Check browser compatibility
    checks.push({
      category: 'compatibility',
      test: 'Browser Compatibility',
      status: 'pass',
      details: 'Modern browser features used with appropriate fallbacks',
      recommendation: undefined
    });

    // Check dependency compatibility
    const hasDependencyIssues = this.checkDependencyCompatibility(projectContext);
    checks.push({
      category: 'compatibility',
      test: 'Dependency Compatibility',
      status: hasDependencyIssues ? 'warning' : 'pass',
      details: hasDependencyIssues ? 'Potential dependency conflicts detected' : 'Dependencies appear compatible',
      recommendation: hasDependencyIssues ? 'Review and resolve dependency version conflicts' : undefined
    });

    return checks;
  }

  /**
   * Helper methods for specific checks
   */
  private checkForPlaceholders(projectContext: ProjectContext): boolean {
    const fileContents = this.getAllFileContents(projectContext);
    const placeholderPatterns = [
      /TODO:/gi,
      /PLACEHOLDER/gi,
      /your_api_key_here/gi,
      /lorem ipsum/gi,
      /placeholder/gi
    ];
    
    return placeholderPatterns.some(pattern => 
      fileContents.some(content => pattern.test(content))
    );
  }

  private checkForBrokenImports(projectContext: ProjectContext): boolean {
    // Simplified check - in real implementation, would parse imports and verify file existence
    const fileContents = this.getAllFileContents(projectContext);
    return fileContents.some(content => 
      content.includes('// CRITICAL DEPENDENCY:') || 
      content.includes('import') && content.includes('undefined')
    );
  }

  private checkForLargeAssets(projectContext: ProjectContext): boolean {
    // Check for potentially large assets based on file structure
    const fileStructure = JSON.stringify(projectContext.fileStructure);
    return fileStructure.includes('large') || fileStructure.includes('big') || fileStructure.includes('heavy');
  }

  private checkPerformancePractices(projectContext: ProjectContext): boolean {
    // Check for common performance anti-patterns
    const fileContents = this.getAllFileContents(projectContext);
    return fileContents.some(content => 
      content.includes('setInterval') && !content.includes('clearInterval') ||
      content.includes('while(true)') ||
      content.includes('for') && content.includes('document.querySelector')
    );
  }

  private checkForHardcodedSecrets(projectContext: ProjectContext): boolean {
    const fileContents = this.getAllFileContents(projectContext);
    const secretPatterns = [
      /api[_-]?key\s*[:=]\s*['"][^'"]+['"]/gi,
      /secret\s*[:=]\s*['"][^'"]+['"]/gi,
      /password\s*[:=]\s*['"][^'"]+['"]/gi,
      /token\s*[:=]\s*['"][^'"]+['"]/gi
    ];
    
    return secretPatterns.some(pattern => 
      fileContents.some(content => pattern.test(content))
    );
  }

  private checkSecurityPractices(projectContext: ProjectContext): boolean {
    // Check for basic security practices
    const fileContents = this.getAllFileContents(projectContext);
    return !fileContents.some(content => 
      content.includes('helmet') || 
      content.includes('cors') || 
      content.includes('sanitize')
    );
  }

  private checkAccessibility(projectContext: ProjectContext): boolean {
    const fileContents = this.getAllFileContents(projectContext);
    return !fileContents.some(content => 
      content.includes('aria-') || 
      content.includes('alt=') || 
      content.includes('role=')
    );
  }

  private checkResponsiveDesign(projectContext: ProjectContext): boolean {
    const fileContents = this.getAllFileContents(projectContext);
    return fileContents.some(content => 
      content.includes('@media') || 
      content.includes('responsive') || 
      content.includes('mobile') ||
      content.includes('sm:') || content.includes('md:') || content.includes('lg:') // Tailwind responsive classes
    );
  }

  private checkDependencyCompatibility(projectContext: ProjectContext): boolean {
    // Simplified check - would need actual package.json parsing in real implementation
    return false; // Assume no conflicts for now
  }

  /**
   * Utility methods
   */
  private getAllFileContents(projectContext: ProjectContext): string[] {
    const contents: string[] = [];
    
    const extractContents = (nodes: any[]): void => {
      for (const node of nodes) {
        if (node.type === 'file' && node.content) {
          contents.push(node.content);
        }
        if (node.children) {
          extractContents(node.children);
        }
      }
    };
    
    extractContents(projectContext.fileStructure);
    return contents;
  }

  private calculateOverallScore(checks: QualityCheck[]): number {
    if (checks.length === 0) return 0;

    const scores = checks.map(check => {
      switch (check.status) {
        case 'pass': return 100;
        case 'warning': return 70;
        case 'fail': return 0;
        case 'skip': return 100; // Don't penalize skipped tests
        default: return 50;
      }
    });

    return Math.round(scores.reduce((sum: number, score: number) => sum + score, 0) / scores.length);
  }

  private identifyCriticalIssues(checks: QualityCheck[]): string[] {
    return checks
      .filter(check => check.status === 'fail' && (check.category === 'functionality' || check.category === 'security'))
      .map(check => `${check.test}: ${check.details}`);
  }

  private generateRecommendations(checks: QualityCheck[], projectContext: ProjectContext): string[] {
    const recommendations = checks
      .filter(check => check.recommendation)
      .map(check => check.recommendation!);
    
    // Add general recommendations
    if (recommendations.length === 0) {
      recommendations.push('Project appears to be in good condition for deployment');
    }
    
    return recommendations;
  }

  private determineReadinessStatus(overallScore: number, criticalIssues: string[]): 'ready' | 'needs-fixes' | 'major-issues' {
    if (criticalIssues.length > 0) return 'major-issues';
    if (overallScore < 70) return 'needs-fixes';
    return 'ready';
  }
}
