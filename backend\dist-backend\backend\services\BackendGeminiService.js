import { GoogleGenAI } from "@google/genai";
import { LicenseType } from '../../types.js';
import { SYSTEM_INSTRUCTION_PLANNER, SYSTEM_INSTRUCTION_FEEDBACK_TASK_GENERATOR, SYSTEM_INSTRUCTION_PLAN_REVIEWER, SYSTEM_INSTRUCTION_BUILD_VALIDATOR } from '../constants.js';
// Error classes
export class JsonParseError extends Error {
    constructor(message, attemptedJsonStr, parsedData, parserErrorMessage) {
        super(message);
        this.attemptedJsonStr = attemptedJsonStr;
        this.parsedData = parsedData;
        this.parserErrorMessage = parserErrorMessage;
        this.name = 'JsonParseError';
    }
}
export class RateLimitError extends Error {
    constructor(message, modelId) {
        super(message);
        this.modelId = modelId;
        this.name = 'RateLimitError';
    }
}
// Validation functions
const isValidGeminiJsonPlannerTask = (task) => {
    return typeof task === 'object' && task !== null &&
        'description' in task && typeof task.description === 'string' &&
        (typeof task.id === 'undefined' || typeof task.id === 'string') &&
        (typeof task.details === 'undefined' || typeof task.details === 'string') &&
        (typeof task.priority === 'undefined' || ['low', 'medium', 'high'].includes(task.priority)) &&
        (typeof task.dependencies === 'undefined' || Array.isArray(task.dependencies)) &&
        (typeof task.estimatedComplexity === 'undefined' || ['low', 'medium', 'high', 'unknown'].includes(task.estimatedComplexity));
};
/**
 * Backend version of GeminiService that runs on the server
 * This handles all AI API calls and processing that was previously done in the browser
 */
export class BackendGeminiService {
    constructor(apiKey) {
        this.DEFAULT_MAX_JSON_RETRIES = 2;
        this.RATE_LIMIT_MAX_RETRIES = 3;
        this.INITIAL_BACKOFF_MS = 3000;
        this.MAX_BACKOFF_MS = 60000;
        if (!apiKey) {
            throw new Error("API Key is required to initialize BackendGeminiService.");
        }
        this.ai = new GoogleGenAI({ apiKey });
    }
    /**
     * Parse JSON text with error handling
     */
    parseJsonText(text) {
        try {
            return JSON.parse(text);
        }
        catch (error) {
            throw new JsonParseError("Failed to parse AI response as JSON", text, "", error instanceof Error ? error.message : String(error));
        }
    }
    /**
     * Parse and validate JSON response
     */
    parseAndValidateJsonResponse(response, schemaValidator, rawTextFromResponse) {
        const textToParse = rawTextFromResponse || response.text || '';
        const parsedData = this.parseJsonText(textToParse);
        if (schemaValidator(parsedData)) {
            return parsedData;
        }
        else {
            console.error("Parsed JSON does not match expected schema. Parsed data:", parsedData, "Original response text:", textToParse);
            throw new JsonParseError("AI response JSON parsed but does not match expected data structure.", textToParse, JSON.stringify(parsedData), "Schema validation failed after successful parsing.");
        }
    }
    /**
     * Serialize file structure for prompts
     */
    serializeFileStructureForPrompt(fileStructure, includeContent = false) {
        let structureString = "Project File Structure Overview:\n";
        const traverse = (nodes, depth) => {
            for (const node of nodes) {
                let typeInfo = node.type;
                if (node.isTestFile)
                    typeInfo += ' (test file)';
                const indent = '  '.repeat(depth);
                structureString += `${indent}- ${node.name} (${typeInfo})`;
                if (includeContent && node.type === 'file' && node.content) {
                    const contentPreview = node.content.length > 200
                        ? node.content.substring(0, 200) + '...'
                        : node.content;
                    structureString += `\n${indent}  Content: ${contentPreview}`;
                }
                structureString += '\n';
                if (node.children && node.children.length > 0) {
                    traverse(node.children, depth + 1);
                }
            }
        };
        traverse(fileStructure, 0);
        return structureString;
    }
    /**
     * Serialize planner tasks for prompts
     */
    serializePlannerTasksForPrompt(tasks) {
        return `Initial Plan - Tasks Overview:\n${tasks.map((t, i) => `${i + 1}. ${t.description} (${t.details || 'No specific file'}) [Priority: ${t.priority || 'medium'}, Complexity: ${t.estimatedComplexity || 'unknown'}]`).join('\n')}`;
    }
    /**
     * Make request with retry logic
     */
    async makeRequestWithRetry(modelName, originalPrompt, systemInstruction, schemaValidator, temperature = 0.5, isCodeGenerationType = false) {
        let currentAttempt = 0;
        let lastError = null;
        let maxRetriesForCurrentErrorType = this.DEFAULT_MAX_JSON_RETRIES;
        while (currentAttempt <= maxRetriesForCurrentErrorType) {
            try {
                let promptForThisAttempt = originalPrompt;
                if (currentAttempt > 0 && lastError instanceof JsonParseError) {
                    console.warn(`Retry ${currentAttempt}/${maxRetriesForCurrentErrorType} for JSON processing with model ${modelName}.`);
                    const specificErrorMsg = lastError.parserErrorMessage;
                    const problematicString = lastError.attemptedJsonStr;
                    promptForThisAttempt = `${originalPrompt}\n\nIMPORTANT: Your previous response had a JSON parsing error: "${specificErrorMsg}". Please ensure your response is valid JSON. Previous problematic response: "${problematicString.substring(0, 500)}..."`;
                }
                const response = await this.ai.models.generateContent({
                    model: modelName,
                    contents: promptForThisAttempt,
                    config: {
                        systemInstruction: systemInstruction,
                        responseMimeType: "application/json",
                        temperature: temperature,
                    }
                });
                const rawTextFromResponse = response.text || '';
                if (isCodeGenerationType) {
                    const parsedData = this.parseJsonText(rawTextFromResponse);
                    let objectToValidate = null;
                    if (typeof parsedData === 'object' && parsedData !== null && 'code' in parsedData) {
                        objectToValidate = parsedData;
                    }
                    else if (typeof parsedData === 'string') {
                        objectToValidate = { code: parsedData };
                    }
                    else {
                        throw new JsonParseError("Code generation response is neither a string nor an object with 'code' property", rawTextFromResponse, JSON.stringify(parsedData), "Invalid structure for code generation");
                    }
                    if (schemaValidator(objectToValidate)) {
                        return objectToValidate;
                    }
                    else {
                        throw new JsonParseError("Code generation response does not match expected schema", rawTextFromResponse, JSON.stringify(objectToValidate), "Schema validation failed");
                    }
                }
                else {
                    return this.parseAndValidateJsonResponse(response, schemaValidator, rawTextFromResponse);
                }
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));
                currentAttempt++;
                if (lastError.message.includes("429") || lastError.message.toLowerCase().includes("rate limit")) {
                    lastError = new RateLimitError(lastError.message, modelName);
                    maxRetriesForCurrentErrorType = this.RATE_LIMIT_MAX_RETRIES;
                }
                if (currentAttempt > maxRetriesForCurrentErrorType)
                    break;
                // Backoff delay
                let delay;
                if (lastError instanceof RateLimitError) {
                    delay = Math.min(this.MAX_BACKOFF_MS, this.INITIAL_BACKOFF_MS * Math.pow(2, currentAttempt - 1));
                    delay += Math.random() * 1000;
                }
                else if (lastError instanceof JsonParseError) {
                    delay = Math.min(this.MAX_BACKOFF_MS / 2, (this.INITIAL_BACKOFF_MS / 2) * Math.pow(2, currentAttempt - 1));
                    delay += Math.random() * 500;
                }
                else {
                    delay = Math.min(this.MAX_BACKOFF_MS / 3, (this.INITIAL_BACKOFF_MS / 3) * Math.pow(2, currentAttempt - 1));
                    delay += Math.random() * 500;
                }
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        if (lastError instanceof RateLimitError)
            throw lastError;
        if (lastError?.message.includes("429"))
            throw new RateLimitError(lastError.message, modelName);
        throw lastError || new Error(`Exhausted retries for AI request with model ${modelName}. Unknown error.`);
    }
    /**
     * Generate initial project plan
     */
    async generateInitialPlan(projectIdea, modelName, projectContext, licenseInfo) {
        let originalPrompt = `Project Idea: "${projectIdea}"`;
        if (projectContext) {
            originalPrompt += `\n\nFull Project Context (for awareness, includes idea and license info if set):\n${projectContext}`;
        }
        if (licenseInfo) {
            originalPrompt += `\n\nLicense Information for this Project:
Type: ${licenseInfo.type}`;
            if (licenseInfo.type === LicenseType.Proprietary && licenseInfo.authorship) {
                originalPrompt += `
Author: ${licenseInfo.authorship.fullName}
Year: ${licenseInfo.authorship.copyrightYear}
Contact: ${licenseInfo.authorship.email}
${licenseInfo.authorship.website ? `Website: ${licenseInfo.authorship.website}` : ''}`;
            }
            originalPrompt += `\n(Ensure a task is created for the LICENSE file based on this.)`;
        }
        else {
            originalPrompt += `\n\nLicense Information: Not specified yet or default (e.g. MIT if not proprietary). Ensure a generic LICENSE file task if appropriate.`;
        }
        return this.makeRequestWithRetry(modelName, originalPrompt, SYSTEM_INSTRUCTION_PLANNER, (data) => {
            return typeof data === 'object' && data !== null &&
                'tasks' in data && Array.isArray(data.tasks) &&
                ('fileStructure' in data ? Array.isArray(data.fileStructure) : true) &&
                data.tasks.every(isValidGeminiJsonPlannerTask) &&
                (!data.fileStructure || data.fileStructure.every((f) => typeof f === 'object' && f !== null && 'name' in f && 'type' in f)) &&
                (data.technologyStackSuggestion === null || typeof data.technologyStackSuggestion === 'undefined' || typeof data.technologyStackSuggestion === 'string');
        }, 0.3);
    }
    /**
     * Review and refine project plan
     */
    async reviewAndRefinePlan(projectIdea, initialTasks, initialFileStructure, projectContext, modelName, currentTechnologyStackSuggestion) {
        const initialTasksPrompt = this.serializePlannerTasksForPrompt(initialTasks);
        const initialFileStructurePrompt = `Initial Plan - File Structure Overview:\n${JSON.stringify(initialFileStructure, null, 2).substring(0, 2000)}...\n(Review and refine this structure if needed based on task adjustments.)`;
        const techStackPrompt = currentTechnologyStackSuggestion
            ? `Current Technology Stack Suggestion: ${currentTechnologyStackSuggestion}`
            : 'No technology stack suggestion yet.';
        const originalPrompt = `
      Original Project Idea: "${projectIdea}"
      ---
      Current Project Context (includes idea, license info, and any initial stack suggestion):
      ${projectContext}
      ---
      Initial Plan to Review:
      ${initialTasksPrompt}
      ---
      ${initialFileStructurePrompt}
      ---
      ${techStackPrompt}
      ---
      Critically review this entire initial plan (tasks, file structure, and technology stack suggestion).
      If it's good, return it as is or with minor tweaks (optionally with "reviewNotes").
      If it needs improvement (missing tasks, illogical structure, inappropriate/missing stack, etc.), provide a NEW, COMPLETE, and REFINED plan (new "tasks", "fileStructure", and "technologyStackSuggestion") and "reviewNotes" explaining changes.
      Ensure all essential boilerplate files are included for the project type and the (potentially new) technology stack. For tasks, ensure "id", "description", "details" are present and estimate "priority", "dependencies", and "estimatedComplexity".
      Ensure a task for the LICENSE file is present and correctly reflects the licenseInfo in the project context.
    `;
        return this.makeRequestWithRetry(modelName, originalPrompt, SYSTEM_INSTRUCTION_PLAN_REVIEWER, (data) => {
            return typeof data === 'object' && data !== null &&
                'tasks' in data && Array.isArray(data.tasks) &&
                ('fileStructure' in data ? Array.isArray(data.fileStructure) : true) &&
                data.tasks.every(isValidGeminiJsonPlannerTask) &&
                (!data.fileStructure || data.fileStructure.every((f) => typeof f === 'object' && f !== null && 'name' in f && 'type' in f)) &&
                (data.technologyStackSuggestion === null || typeof data.technologyStackSuggestion === 'undefined' || typeof data.technologyStackSuggestion === 'string');
        }, 0.4);
    }
    /**
     * Generate tasks from user feedback
     */
    async generateTasksFromUserFeedback(projectContext, projectIdea, fileStructure, feedback, modelName) {
        const fileStructurePrompt = this.serializeFileStructureForPrompt(fileStructure);
        const originalPrompt = `
      Original Project Idea: ${projectIdea}
      User Feedback Received:
        Type: ${feedback.type}
        File Path (if any): ${feedback.filePath || 'N/A'}
        Description: ${feedback.description}
      ---
      Current Project Full Context (includes this feedback and licenseInfo): ${projectContext}
      ---
      ${fileStructurePrompt}
      ---
      Based on the user feedback, original idea, context, and file structure, generate a list of specific tasks to address the feedback.
      For each task, include "id", "description", "details", and estimate "priority", "dependencies", and "estimatedComplexity".
      Ensure image-related feedback tasks have descriptions like "Generate image: ..." or "Update image: ..." and the filename in "details".
      If feedback pertains to licensing, generate appropriate tasks (e.g., "Update LICENSE file").
    `;
        return this.makeRequestWithRetry(modelName, originalPrompt, SYSTEM_INSTRUCTION_FEEDBACK_TASK_GENERATOR, (data) => {
            return typeof data === 'object' && data !== null &&
                'tasks' in data && Array.isArray(data.tasks) &&
                data.tasks.every(isValidGeminiJsonPlannerTask) &&
                !('fileStructure' in data);
        }, 0.4);
    }
    /**
     * Validate project build
     */
    async validateProjectBuild(projectContext, fileStructure, packageJsonContent, modelName) {
        const fileStructurePrompt = this.serializeFileStructureForPrompt(fileStructure, false);
        const originalPrompt = `
      Project Context: ${projectContext}
      ---
      ${fileStructurePrompt}
      ---
      package.json content (if available):
      \`\`\`json
      ${packageJsonContent || "{ \"note\": \"package.json not found or empty\" }"}
      \`\`\`
      ---
      Perform a conceptual build check.
      Infer project type. Analyze configuration files (package.json, tsconfig.json if implied by TS files).
      Conceptually check for common type errors (if TypeScript) or Node.js startup issues.
      Return validation results with any issues found.
    `;
        return this.makeRequestWithRetry(modelName, originalPrompt, SYSTEM_INSTRUCTION_BUILD_VALIDATOR, (data) => {
            return typeof data === 'object' && data !== null &&
                'buildIssues' in data && Array.isArray(data.buildIssues) &&
                'validationSummary' in data && typeof data.validationSummary === 'string' &&
                (typeof data.buildCommand === 'undefined' || typeof data.buildCommand === 'string') &&
                (typeof data.projectType === 'undefined' || typeof data.projectType === 'string');
        }, 0.3);
    }
}
//# sourceMappingURL=BackendGeminiService.js.map