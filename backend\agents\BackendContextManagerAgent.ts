import { BackendGeminiService } from '../services/BackendGeminiService.js';
import { TaskProcessingStage } from '../../types.js';

/**
 * Interface for backend logging that sends updates to frontend
 */
interface BackendLoggingInterface {
  addCompanyLog: (agent: string, message: string, status: 'info' | 'success' | 'error' | 'working', taskId?: string) => Promise<void>;
  addTaskLog: (taskId: string, agent: string, message: string, status: 'info' | 'success' | 'error' | 'working', stage: TaskProcessingStage, attachments?: Array<{title: string, content: string, isCodeBlock?: boolean}>) => Promise<void>;
  addDecisionLogEntry: (agent: string, decision: string, reasoning: string, stackTrace?: string, taskId?: string) => Promise<void>;
}

/**
 * Response interface for context updates
 */
interface ContextUpdateResponse {
  updatedContext: string;
  summary?: string;
}

/**
 * Backend version of ContextManagerAgent that runs on the server
 * Handles project context management and updates
 */
export class BackendContextManagerAgent {
  private geminiService: BackendGeminiService;
  private loggingInterface: BackendLoggingInterface;

  constructor(geminiService: BackendGeminiService, loggingInterface: BackendLoggingInterface) {
    this.geminiService = geminiService;
    this.loggingInterface = loggingInterface;
  }

  /**
   * Log activity to the frontend
   */
  private async logActivity(message: string, status: 'info' | 'success' | 'error' | 'working'): Promise<void> {
    await this.loggingInterface.addCompanyLog('Context Manager Agent', message, status);
  }

  /**
   * Log decision to the frontend
   */
  private logDecision(decision: string, reasoning: string, stackTrace?: string, taskId?: string): void {
    // Fire and forget - don't await to avoid blocking
    this.loggingInterface.addDecisionLogEntry('Context Manager Agent', decision, reasoning, stackTrace, taskId);
  }

  /**
   * Updates the project context with new information.
   * @param currentContext - The current project context.
   * @param newInformation - New information to integrate into the context.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the updated context response.
   */
  public async updateProjectContext(
    currentContext: string,
    newInformation: string,
    modelName: string
  ): Promise<ContextUpdateResponse> {
    try {
      await this.logActivity('Updating project context with new information...', 'working');
      this.logDecision('Context Update Started', `Adding new information: ${newInformation.substring(0, 100)}...`, `Using model: ${modelName}`);

      const originalPrompt = `
        Current Project Context:
        ${currentContext}
        
        New Information to Integrate:
        ${newInformation}
        
        Please update the project context by integrating the new information. 
        Maintain the existing context structure while adding the new information in a logical way.
        Ensure the updated context remains coherent and well-organized.
        
        Return a JSON object with:
        - "updatedContext": The complete updated project context
        - "summary": A brief summary of what was added/changed (optional)
      `;

      const response = await this.geminiService.makeRequestWithRetry(
        modelName,
        originalPrompt,
        "You are a Context Manager Agent. Update the project context by integrating new information while maintaining coherence and organization. Return a JSON object with 'updatedContext' and optionally 'summary'.",
        (data: any): data is ContextUpdateResponse => {
          return typeof data === 'object' && data !== null &&
                 'updatedContext' in data && typeof data.updatedContext === 'string' &&
                 (typeof data.summary === 'undefined' || typeof data.summary === 'string');
        },
        0.3
      );

      await this.logActivity('Project context updated successfully', 'success');
      this.logDecision('Context Update Completed', `Updated context with new information. Summary: ${response.summary || 'No summary provided'}`, 'Context update completed successfully');

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Failed to update project context: ${errorMessage}`, 'error');
      this.logDecision('Context Update Failed', `Error: ${errorMessage}`, 'Context update encountered an error');
      console.error("BackendContextManagerAgent: Error updating context -", error);
      throw error;
    }
  }

  /**
   * Analyzes the current project context and provides insights.
   * @param currentContext - The current project context.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to context analysis insights.
   */
  public async analyzeProjectContext(
    currentContext: string,
    modelName: string
  ): Promise<{
    insights: string[];
    recommendations: string[];
    potentialIssues: string[];
  }> {
    try {
      await this.logActivity('Analyzing project context for insights...', 'working');
      this.logDecision('Context Analysis Started', 'Analyzing current project context for insights and recommendations', `Using model: ${modelName}`);

      const originalPrompt = `
        Project Context to Analyze:
        ${currentContext}
        
        Please analyze this project context and provide:
        1. Key insights about the project's current state
        2. Recommendations for improvement or next steps
        3. Potential issues or concerns that should be addressed
        
        Return a JSON object with:
        - "insights": Array of key insights about the project
        - "recommendations": Array of recommendations for improvement
        - "potentialIssues": Array of potential issues or concerns
      `;

      const response = await this.geminiService.makeRequestWithRetry(
        modelName,
        originalPrompt,
        "You are a Context Manager Agent. Analyze the project context and provide insights, recommendations, and identify potential issues. Return a JSON object with arrays for insights, recommendations, and potentialIssues.",
        (data: any): data is { insights: string[]; recommendations: string[]; potentialIssues: string[] } => {
          return typeof data === 'object' && data !== null &&
                 'insights' in data && Array.isArray(data.insights) &&
                 'recommendations' in data && Array.isArray(data.recommendations) &&
                 'potentialIssues' in data && Array.isArray(data.potentialIssues) &&
                 data.insights.every((item: any) => typeof item === 'string') &&
                 data.recommendations.every((item: any) => typeof item === 'string') &&
                 data.potentialIssues.every((item: any) => typeof item === 'string');
        },
        0.4
      );

      await this.logActivity(`Context analysis completed. Found ${response.insights.length} insights, ${response.recommendations.length} recommendations, ${response.potentialIssues.length} potential issues`, 'success');
      this.logDecision('Context Analysis Completed', `Analysis complete: ${response.insights.length} insights, ${response.recommendations.length} recommendations, ${response.potentialIssues.length} issues identified`, 'Context analysis completed successfully');

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Failed to analyze project context: ${errorMessage}`, 'error');
      this.logDecision('Context Analysis Failed', `Error: ${errorMessage}`, 'Context analysis encountered an error');
      console.error("BackendContextManagerAgent: Error analyzing context -", error);
      throw error;
    }
  }

  /**
   * Summarizes the project context for easier understanding.
   * @param currentContext - The current project context.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to a context summary.
   */
  public async summarizeProjectContext(
    currentContext: string,
    modelName: string
  ): Promise<{
    summary: string;
    keyPoints: string[];
    status: string;
  }> {
    try {
      await this.logActivity('Generating project context summary...', 'working');
      this.logDecision('Context Summary Started', 'Generating summary of current project context', `Using model: ${modelName}`);

      const originalPrompt = `
        Project Context to Summarize:
        ${currentContext}
        
        Please provide a concise summary of this project context including:
        1. A brief overall summary
        2. Key points and milestones
        3. Current project status
        
        Return a JSON object with:
        - "summary": A brief overall summary of the project
        - "keyPoints": Array of key points and milestones
        - "status": Current project status (e.g., "Planning", "In Development", "Testing", "Complete")
      `;

      const response = await this.geminiService.makeRequestWithRetry(
        modelName,
        originalPrompt,
        "You are a Context Manager Agent. Summarize the project context concisely. Return a JSON object with summary, keyPoints array, and status.",
        (data: any): data is { summary: string; keyPoints: string[]; status: string } => {
          return typeof data === 'object' && data !== null &&
                 'summary' in data && typeof data.summary === 'string' &&
                 'keyPoints' in data && Array.isArray(data.keyPoints) &&
                 'status' in data && typeof data.status === 'string' &&
                 data.keyPoints.every((item: any) => typeof item === 'string');
        },
        0.3
      );

      await this.logActivity('Project context summary generated successfully', 'success');
      this.logDecision('Context Summary Completed', `Summary generated with ${response.keyPoints.length} key points. Status: ${response.status}`, 'Context summary completed successfully');

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Failed to summarize project context: ${errorMessage}`, 'error');
      this.logDecision('Context Summary Failed', `Error: ${errorMessage}`, 'Context summary encountered an error');
      console.error("BackendContextManagerAgent: Error summarizing context -", error);
      throw error;
    }
  }
}
