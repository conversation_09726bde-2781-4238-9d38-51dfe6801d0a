/**
 * Configuration for autonomous project generation services
 * Includes free APIs and services for complete project autonomy
 */

export interface ServiceConfig {
  name: string;
  baseUrl: string;
  apiKey?: string;
  rateLimit: number; // requests per minute
  features: string[];
  license: string;
  documentation: string;
  isActive: boolean;
}

export interface AudioServiceConfig extends ServiceConfig {
  formats: string[];
  maxDuration: number; // seconds
  categories: string[];
}

export interface AssetServiceConfig extends ServiceConfig {
  supportedTypes: string[];
  maxFileSize: number; // bytes
  dimensions?: string[];
}

export interface DeploymentServiceConfig extends ServiceConfig {
  platforms: string[];
  buildCommands: string[];
  environmentVariables: string[];
}

/**
 * Audio Generation Services
 */
export const AUDIO_SERVICES: Record<string, AudioServiceConfig> = {
  FREESOUND: {
    name: 'Freesound',
    baseUrl: 'https://freesound.org/apiv2',
    apiKey: process.env.FREESOUND_API_KEY,
    rateLimit: 60,
    features: ['sound-effects', 'ambient-sounds', 'music-loops'],
    license: 'Creative Commons',
    documentation: 'https://freesound.org/docs/api/',
    isActive: true,
    formats: ['wav', 'mp3', 'ogg', 'flac'],
    maxDuration: 300,
    categories: ['sfx', 'ambient', 'music', 'voice', 'foley']
  },
  
  MUSICGEN_HUGGINGFACE: {
    name: 'MusicGen (Hugging Face)',
    baseUrl: 'https://api-inference.huggingface.co/models/facebook/musicgen-small',
    apiKey: process.env.HUGGINGFACE_API_KEY,
    rateLimit: 30,
    features: ['ai-music-generation', 'custom-prompts', 'various-genres'],
    license: 'Apache 2.0',
    documentation: 'https://huggingface.co/facebook/musicgen-small',
    isActive: true,
    formats: ['wav'],
    maxDuration: 30,
    categories: ['background-music', 'ambient', 'electronic', 'classical']
  },

  ZAPSPLAT_FREE: {
    name: 'Zapsplat Free Tier',
    baseUrl: 'https://api.zapsplat.com/v1',
    apiKey: process.env.ZAPSPLAT_API_KEY,
    rateLimit: 20,
    features: ['professional-sfx', 'music-tracks', 'voice-overs'],
    license: 'Royalty Free',
    documentation: 'https://zapsplat.com/api-documentation/',
    isActive: false, // Requires paid account for API access
    formats: ['wav', 'mp3'],
    maxDuration: 600,
    categories: ['sfx', 'music', 'voice', 'foley', 'ambient']
  }
};

/**
 * Asset Management Services
 */
export const ASSET_SERVICES: Record<string, AssetServiceConfig> = {
  UNSPLASH: {
    name: 'Unsplash',
    baseUrl: 'https://api.unsplash.com',
    apiKey: process.env.UNSPLASH_API_KEY,
    rateLimit: 50,
    features: ['high-quality-photos', 'search-by-keyword', 'various-categories'],
    license: 'Unsplash License',
    documentation: 'https://unsplash.com/documentation',
    isActive: true,
    supportedTypes: ['jpg', 'png'],
    maxFileSize: ********, // 10MB
    dimensions: ['1920x1080', '1280x720', '800x600', 'custom']
  },

  PIXABAY: {
    name: 'Pixabay',
    baseUrl: 'https://pixabay.com/api',
    apiKey: process.env.PIXABAY_API_KEY,
    rateLimit: 100,
    features: ['photos', 'illustrations', 'vectors', 'videos'],
    license: 'Pixabay License',
    documentation: 'https://pixabay.com/api/docs/',
    isActive: true,
    supportedTypes: ['jpg', 'png', 'svg', 'mp4'],
    maxFileSize: 20971520, // 20MB
    dimensions: ['1920x1080', '1280x720', '640x480', 'custom']
  },

  ICONIFY: {
    name: 'Iconify',
    baseUrl: 'https://api.iconify.design',
    rateLimit: 1000,
    features: ['svg-icons', 'multiple-icon-sets', 'customizable'],
    license: 'Various Open Source',
    documentation: 'https://docs.iconify.design/api/',
    isActive: true,
    supportedTypes: ['svg', 'png'],
    maxFileSize: 1048576, // 1MB
    dimensions: ['16x16', '24x24', '32x32', '48x48', '64x64', 'custom']
  },

  GOOGLE_FONTS: {
    name: 'Google Fonts',
    baseUrl: 'https://www.googleapis.com/webfonts/v1/webfonts',
    apiKey: process.env.GOOGLE_FONTS_API_KEY,
    rateLimit: 1000,
    features: ['web-fonts', 'font-families', 'multiple-weights'],
    license: 'Open Font License',
    documentation: 'https://developers.google.com/fonts/docs/developer_api',
    isActive: true,
    supportedTypes: ['woff2', 'woff', 'ttf'],
    maxFileSize: 5242880 // 5MB
  }
};

/**
 * Deployment Services
 */
export const DEPLOYMENT_SERVICES: Record<string, DeploymentServiceConfig> = {
  NETLIFY: {
    name: 'Netlify',
    baseUrl: 'https://api.netlify.com/api/v1',
    apiKey: process.env.NETLIFY_API_KEY,
    rateLimit: 500,
    features: ['static-hosting', 'continuous-deployment', 'custom-domains', 'https'],
    license: 'Commercial',
    documentation: 'https://docs.netlify.com/api/get-started/',
    isActive: true,
    platforms: ['web', 'spa', 'static'],
    buildCommands: ['npm run build', 'yarn build'],
    environmentVariables: ['NODE_ENV', 'REACT_APP_*', 'VITE_*']
  },

  VERCEL: {
    name: 'Vercel',
    baseUrl: 'https://api.vercel.com',
    apiKey: process.env.VERCEL_API_KEY,
    rateLimit: 100,
    features: ['static-hosting', 'serverless-functions', 'edge-functions', 'analytics'],
    license: 'Commercial',
    documentation: 'https://vercel.com/docs/rest-api',
    isActive: true,
    platforms: ['web', 'spa', 'serverless'],
    buildCommands: ['npm run build', 'yarn build', 'next build'],
    environmentVariables: ['NODE_ENV', 'NEXT_PUBLIC_*', 'VITE_*']
  },

  GITHUB_PAGES: {
    name: 'GitHub Pages',
    baseUrl: 'https://api.github.com',
    apiKey: process.env.GITHUB_API_KEY,
    rateLimit: 5000,
    features: ['static-hosting', 'github-integration', 'custom-domains'],
    license: 'Free',
    documentation: 'https://docs.github.com/en/rest',
    isActive: true,
    platforms: ['web', 'static'],
    buildCommands: ['npm run build', 'jekyll build'],
    environmentVariables: ['GITHUB_TOKEN']
  }
};

/**
 * AI and ML Services
 */
export const AI_SERVICES: Record<string, ServiceConfig> = {
  HUGGINGFACE_INFERENCE: {
    name: 'Hugging Face Inference API',
    baseUrl: 'https://api-inference.huggingface.co',
    apiKey: process.env.HUGGINGFACE_API_KEY,
    rateLimit: 30,
    features: ['text-generation', 'image-generation', 'audio-generation', 'nlp'],
    license: 'Apache 2.0',
    documentation: 'https://huggingface.co/docs/api-inference/',
    isActive: true
  },

  OPENAI_COMPATIBLE: {
    name: 'OpenAI Compatible APIs',
    baseUrl: 'https://api.openai.com/v1',
    apiKey: process.env.OPENAI_API_KEY,
    rateLimit: 60,
    features: ['text-generation', 'code-generation', 'embeddings'],
    license: 'Commercial',
    documentation: 'https://platform.openai.com/docs/api-reference',
    isActive: false // Optional fallback
  }
};

/**
 * Database and Storage Services
 */
export const STORAGE_SERVICES: Record<string, ServiceConfig> = {
  SUPABASE: {
    name: 'Supabase',
    baseUrl: 'https://api.supabase.com',
    apiKey: process.env.SUPABASE_API_KEY,
    rateLimit: 100,
    features: ['postgresql', 'real-time', 'auth', 'storage'],
    license: 'Apache 2.0',
    documentation: 'https://supabase.com/docs/reference/api',
    isActive: true
  },

  FIREBASE: {
    name: 'Firebase',
    baseUrl: 'https://firebase.googleapis.com',
    apiKey: process.env.FIREBASE_API_KEY,
    rateLimit: 100,
    features: ['firestore', 'real-time-database', 'auth', 'hosting'],
    license: 'Commercial',
    documentation: 'https://firebase.google.com/docs/reference/rest',
    isActive: true
  }
};

/**
 * Service Health Check
 */
export async function checkServiceHealth(service: ServiceConfig): Promise<boolean> {
  try {
    const response = await fetch(service.baseUrl, {
      method: 'HEAD',
      headers: service.apiKey ? { 'Authorization': `Bearer ${service.apiKey}` } : {}
    });
    return response.ok;
  } catch {
    return false;
  }
}

/**
 * Get active services by category
 */
export function getActiveServices<T extends ServiceConfig>(services: Record<string, T>): Record<string, T> {
  return Object.fromEntries(
    Object.entries(services).filter(([, service]) => service.isActive)
  );
}

/**
 * Rate limiting helper
 */
export class RateLimiter {
  private requests: Map<string, number[]> = new Map();

  canMakeRequest(serviceKey: string, rateLimit: number): boolean {
    const now = Date.now();
    const requests = this.requests.get(serviceKey) || [];
    
    // Remove requests older than 1 minute
    const recentRequests = requests.filter(time => now - time < 60000);
    
    if (recentRequests.length >= rateLimit) {
      return false;
    }
    
    recentRequests.push(now);
    this.requests.set(serviceKey, recentRequests);
    return true;
  }
}

export const rateLimiter = new RateLimiter();
