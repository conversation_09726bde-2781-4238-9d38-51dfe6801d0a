export var AgentType;
(function (AgentType) {
    AgentType["PLANNER"] = "PLANNER";
    AgentType["CODER"] = "CODER";
    AgentType["LINTER_FORMATTER"] = "LINTER_FORMATTER";
    AgentType["SECURITY_ANALYST"] = "SECURITY_ANALYST";
    AgentType["BUG_HUNTER"] = "BUG_HUNTER";
    AgentType["REFACTORER"] = "REFACTORER";
    AgentType["CONTEXT_MANAGER"] = "CONTEXT_MANAGER";
    AgentType["TESTER"] = "TESTER";
    AgentType["IMAGE_GENERATOR"] = "IMAGE_GENERATOR";
    AgentType["CLARIFIER"] = "CLARIFIER";
    // New specialized agents for 100% autonomy
    AgentType["AUDIO_GENERATOR"] = "AUDIO_GENERATOR";
    AgentType["ASSET_MANAGER"] = "ASSET_MANAGER";
    AgentType["DEPLOYMENT_MANAGER"] = "DEPLOYMENT_MANAGER";
    AgentType["DEPENDENCY_RESOLVER"] = "DEPENDENCY_RESOLVER";
    AgentType["QUALITY_ASSURANCE"] = "QUALITY_ASSURANCE";
    AgentType["GAME_DESIGNER"] = "GAME_DESIGNER";
    AgentType["ANIMATION_SPECIALIST"] = "ANIMATION_SPECIALIST";
    AgentType["PERFORMANCE_OPTIMIZER"] = "PERFORMANCE_OPTIMIZER";
})(AgentType || (AgentType = {}));
export var OperatingPhase;
(function (OperatingPhase) {
    OperatingPhase["AGENT_MODEL_CONFIGURATION"] = "AGENT_MODEL_CONFIGURATION";
    OperatingPhase["API_KEY_INPUT"] = "API_KEY_INPUT";
    OperatingPhase["COMPANY_OPERATIONAL"] = "COMPANY_OPERATIONAL";
})(OperatingPhase || (OperatingPhase = {}));
export var ProjectLifecyclePhase;
(function (ProjectLifecyclePhase) {
    ProjectLifecyclePhase["IDLE"] = "IDLE";
    ProjectLifecyclePhase["AWAITING_PROJECT_IDEA"] = "AWAITING_PROJECT_IDEA";
    ProjectLifecyclePhase["AWAITING_LICENSE_CHOICE"] = "AWAITING_LICENSE_CHOICE";
    ProjectLifecyclePhase["PLANNING"] = "PLANNING";
    ProjectLifecyclePhase["PLAN_REVIEW_PENDING"] = "PLAN_REVIEW_PENDING";
    ProjectLifecyclePhase["PLANNING_USER_FEEDBACK"] = "PLANNING_USER_FEEDBACK";
    ProjectLifecyclePhase["AUTONOMOUS_EXECUTION"] = "AUTONOMOUS_EXECUTION";
    ProjectLifecyclePhase["TESTING_PLANNING"] = "TESTING_PLANNING";
    ProjectLifecyclePhase["TEST_CODE_GENERATION_PENDING"] = "TEST_CODE_GENERATION_PENDING";
    ProjectLifecyclePhase["TEST_ANALYSIS_PENDING"] = "TEST_ANALYSIS_PENDING";
    ProjectLifecyclePhase["AWAITING_TEST_COVERAGE_ANALYSIS"] = "AWAITING_TEST_COVERAGE_ANALYSIS";
    ProjectLifecyclePhase["PROJECT_VERIFIED"] = "PROJECT_VERIFIED";
    ProjectLifecyclePhase["PROJECT_BUILD_VALIDATION"] = "PROJECT_BUILD_VALIDATION";
    ProjectLifecyclePhase["PROJECT_READY_FOR_DEPLOYMENT"] = "PROJECT_READY_FOR_DEPLOYMENT";
    ProjectLifecyclePhase["AWAITING_RATE_LIMIT_RESOLUTION"] = "AWAITING_RATE_LIMIT_RESOLUTION";
    ProjectLifecyclePhase["PROJECT_PAUSED_ON_RATE_LIMIT"] = "PROJECT_PAUSED_ON_RATE_LIMIT";
    ProjectLifecyclePhase["EXECUTION_HALTED_ERROR"] = "EXECUTION_HALTED_ERROR";
})(ProjectLifecyclePhase || (ProjectLifecyclePhase = {}));
export var LicenseType;
(function (LicenseType) {
    LicenseType["MIT"] = "MIT";
    LicenseType["GPLv3"] = "GPLv3";
    LicenseType["Apache2"] = "Apache-2.0";
    LicenseType["Proprietary"] = "Proprietary";
    LicenseType["Unspecified"] = "Unspecified";
})(LicenseType || (LicenseType = {}));
//# sourceMappingURL=types.js.map